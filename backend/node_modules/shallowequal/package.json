{"name": "shallowequal", "version": "1.1.0", "description": "Like lodash isEqualWith but for shallow equal.", "main": "index.js", "scripts": {"lint": "eslint index.js test", "test": "mocha --require babel-register", "build:strip-flow": "flow-remove-types --pretty index.original.js > index.js", "build:gen-flow": "flow gen-flow-files index.original.js > index.js.flow", "build": "npm run build:strip-flow && npm run build:gen-flow", "prepublish": "npm run build && npm run pretty && npm run lint && npm run test", "travis": "npm run lint && npm run test", "pretty": "prettier --write --tab-width 2 'test/**/*.js' '*.{js,js.flow}'", "precommit": "lint-staged"}, "lint-staged": {"*.{js,json,css,js.flow}": ["prettier --write", "git add"]}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/dashed"}, "repository": "dashed/shallowequal", "license": "MIT", "files": ["index.js", "index.js.flow", "index.original.js"], "keywords": ["shallowequal", "shallow", "equal", "isequal", "compare", "isequalwith"], "eslintConfig": {"parser": "babel-es<PERSON>", "env": {"browser": true, "node": true, "mocha": true}, "extends": ["eslint:recommended"]}, "devDependencies": {"babel-eslint": "^8.0.0", "babel-preset-env": "^1.6.1", "babel-register": "^6.24.1", "chai": "^4.0.0", "eslint": "^4.7.1", "flow-bin": "^0.75.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "lint-staged": "^6.0.0", "mocha": "^5.0.0", "prettier": "^1.9.2"}}