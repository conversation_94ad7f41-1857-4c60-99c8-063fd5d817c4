import React from 'react';
import StandardLevelPage from '../components/StandardLevelPage';

const MasterPage: React.FC = () => {
  const pageConfig = {
    level: 'Master',
    title: 'Bourses d\'Études de Master | Financement pour Études Supérieures de 2ème Cycle',
    description: 'Explorez des opportunités de bourses d\'études pour programmes de Master. Financez vos études supérieures avec des bourses internationales prestigieuses.',
    keywords: 'bourses master, financement master, bourses deuxième cycle, études supérieures',
    heroTitle: 'Bourses d\'Études de',
    heroSubtitle: 'Accédez aux programmes de Master les plus prestigieux avec des bourses d\'excellence qui transformeront votre carrière professionnelle.',
    infoTitle: 'Pourquoi Poursuivre un Master avec une Bourse ?',
    infoContent: 'Un Master représente un investissement stratégique dans votre avenir professionnel. Avec une bourse d\'études, vous pouvez vous concentrer pleinement sur l\'excellence académique et la recherche, sans les contraintes financières.',
    benefits: [
      'Spécialisation dans votre domaine d\'expertise',
      'Accès à des laboratoires et équipements de pointe',
      'Encadrement par des professeurs renommés',
      'Opportunités de recherche et de publication',
      'Réseau professionnel international étendu',
      'Préparation optimale pour le doctorat ou l\'industrie'
    ],
    apiEndpoint: '/api/scholarships/search'
  };

  return <StandardLevelPage config={pageConfig} />;
};

export default MasterPage;
