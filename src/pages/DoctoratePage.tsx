import React from 'react';
import StandardLevelPage from '../components/StandardLevelPage';

const DoctoratePage: React.FC = () => {
  const pageConfig = {
    level: 'Doctorat',
    title: 'Bourses d\'Études de Doctorat | Financement pour Recherche Doctorale',
    description: 'Découvrez des bourses d\'études doctorales prestigieuses. Financez votre recherche de troisième cycle et contribuez à l\'avancement des connaissances dans votre domaine.',
    keywords: 'bourses doctorat, financement phd, bourses recherche, études doctorales',
    heroTitle: 'Bourses d\'Études de',
    heroSubtitle: 'Financez votre parcours doctoral et contribuez à l\'avancement des connaissances dans votre domaine d\'expertise.',
    infoTitle: 'Pourquoi Choisir un Doctorat avec Financement ?',
    infoContent: 'Le doctorat représente le plus haut niveau d\'études académiques, ouvrant les portes à une carrière de recherche, d\'enseignement supérieur et d\'innovation. Avec une bourse doctorale, vous pouvez vous consacrer entièrement à votre recherche révolutionnaire.',
    benefits: [
      'Financement complet pendant 3 à 5 ans de recherche',
      'Accès aux équipements et laboratoires de pointe',
      'Mentorat par des chercheurs de renommée mondiale',
      'Opportunités de publication dans des revues prestigieuses',
      'Participation à des conférences internationales',
      'Développement d\'un réseau académique d\'élite'
    ],
    apiEndpoint: '/api/scholarships/search'
  };

  return <StandardLevelPage config={pageConfig} />;
};

export default DoctoratePage;
