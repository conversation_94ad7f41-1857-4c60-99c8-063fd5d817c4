import React from 'react';
import StandardLevelPage from '../components/StandardLevelPage';

const UndergraduatePage: React.FC = () => {
  const pageConfig = {
    level: 'Licence',
    title: 'Bourses d\'Études de Licence | Opportunités de Financement pour Étudiants de Premier Cycle',
    description: 'Découvrez des centaines de bourses d\'études pour étudiants de licence. Financez vos études de premier cycle avec des opportunités de bourses internationales et nationales.',
    keywords: 'bourses licence, financement études, bourses premier cycle, aide financière étudiants',
    heroTitle: 'Bourses d\'Études de',
    heroSubtitle: 'Découvrez les meilleures opportunités de financement pour vos études de premier cycle et transformez votre avenir académique.',
    infoTitle: 'Pourquoi Choisir une Bourse de Licence ?',
    infoContent: 'Les études de licence représentent la première étape cruciale de votre parcours académique supérieur. Avec les coûts d\'éducation en constante augmentation, obtenir une bourse d\'études peut transformer votre avenir académique et professionnel.',
    benefits: [
      'Accès à des universités prestigieuses sans le fardeau financier',
      'Concentration totale sur vos études sans stress financier',
      'Développement d\'un réseau international de contacts',
      'Expérience interculturelle enrichissante',
      'Amélioration significative de votre CV pour votre future carrière',
      'Opportunités de stages et d\'emploi exclusives'
    ],
    apiEndpoint: '/api/scholarships/search'
  };

  return <StandardLevelPage config={pageConfig} />;
};

export default UndergraduatePage;
