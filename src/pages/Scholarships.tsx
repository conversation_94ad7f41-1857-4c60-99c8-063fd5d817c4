import React, { useState, useEffect } from 'react';
import { Pagination, Spin, Alert } from 'antd';
import { useLocation } from 'react-router-dom';
import EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';
import ProfessionalSidebar from '../components/ProfessionalSidebar';
import PageEndSuggestions from '../components/PageEndSuggestions';
import AdPlacement from '../components/AdPlacement';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const Scholarships: React.FC = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 9, // Show 9 scholarships per page (3x3 grid)
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Read URL parameters on component mount
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const levelParam = searchParams.get('level');
    const countryParam = searchParams.get('country');

    if (levelParam) {
      setSelectedLevel(levelParam);
    }
    if (countryParam) {
      setSelectedCountry(countryParam);
    }
  }, [location.search]);

  // Fetch scholarships with pagination and filters
  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);

  const fetchScholarships = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());

      if (searchQuery) {
        params.append('q', searchQuery);
      }

      if (selectedLevel) {
        params.append('level', selectedLevel);
      }

      if (selectedCountry) {
        params.append('country', selectedCountry);
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      console.log('Scholarships search API response:', data);

      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }
      const scholarshipsData = data.data || data.scholarships || [];
      const paginationData = data.pagination || {};

      setScholarships(scholarshipsData);
      setPagination(paginationData || {
        total: scholarshipsData.length || 0,
        page: 1,
        limit: 9,
        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),
        hasNextPage: false,
        hasPreviousPage: false
      });
      setError(null);
    } catch (err) {
      console.error('Error fetching scholarships:', err);
      setError('Failed to load scholarships. Please try again later.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      page
    }));
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-blue-900 via-primary-dark to-primary overflow-hidden">
        {/* Background overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply" />

        <div className="max-w-7xl mx-auto py-20 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6 animate-fade-in">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              {pagination.total} bourses disponibles
            </div>

            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in">
              Trouvez Votre
              <span className="block text-yellow-300">Bourse Idéale</span>
            </h1>

            <p className="mt-5 max-w-2xl mx-auto text-xl text-white/80 animate-slide-up">
              Accédez à la plus grande base de données de bourses d'études au monde.
              Des opportunités de financement pour tous les niveaux d'études, dans tous les pays,
              pour réaliser vos rêves académiques sans contraintes financières.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12 animate-fade-in">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-yellow-300 mb-2">1000+</div>
                <div className="text-blue-100">Bourses Actives</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-yellow-300 mb-2">60+</div>
                <div className="text-blue-100">Pays Couverts</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-yellow-300 mb-2">24/7</div>
                <div className="text-blue-100">Mise à Jour</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Ad Placement - Top Banner */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block">
        <AdPlacement
          adSlot="1234567890"
          adSize="leaderboard"
          responsive={true}
          fullWidth={true}
        />
      </div>

      {/* Search and Filter Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10">
        <div className="bg-white shadow-xl rounded-2xl p-8 mb-12 transform translate-y-0 animate-slide-up">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Filtrer les Bourses</h2>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedLevel('');
                setSelectedCountry('');
                setPagination(prev => ({ ...prev, page: 1 }));
              }}
              className="text-sm text-primary hover:text-primary-dark font-medium flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Réinitialiser les filtres
            </button>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700">
                Recherche
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  name="search"
                  id="search"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setPagination(prev => ({ ...prev, page: 1 }));
                  }}
                  className="pl-10 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                  placeholder="Rechercher des bourses..."
                />
              </div>
            </div>
            <div>
              <label htmlFor="level" className="block text-sm font-medium text-gray-700">
                Niveau d'Études
              </label>
              <select
                id="level"
                name="level"
                value={selectedLevel}
                onChange={(e) => {
                  setSelectedLevel(e.target.value);
                  setPagination(prev => ({ ...prev, page: 1 }));
                }}
                className="mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm"
              >
                <option value="">Tous les niveaux</option>
                <option value="Licence">Licence</option>
                <option value="Master">Master</option>
                <option value="Doctorat">Doctorat</option>
                <option value="Post-doctorat">Post-doctorat</option>
              </select>
            </div>
            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                Pays
              </label>
              <select
                id="country"
                name="country"
                value={selectedCountry}
                onChange={(e) => {
                  setSelectedCountry(e.target.value);
                  setPagination(prev => ({ ...prev, page: 1 }));
                }}
                className="mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm"
              >
                <option value="">Tous les pays</option>
                <option value="France">France</option>
                <option value="Canada">Canada</option>
                <option value="Belgique">Belgique</option>
                <option value="Suisse">Suisse</option>
                <option value="Maroc">Maroc</option>
                <option value="Tunisie">Tunisie</option>
                <option value="Sénégal">Sénégal</option>
                <option value="Côte d'Ivoire">Côte d'Ivoire</option>
              </select>
            </div>
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Statut
              </label>
              <select
                id="status"
                name="status"
                className="mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm"
              >
                <option value="">Tous les statuts</option>
                <option value="open">Ouvertes</option>
                <option value="closed">Fermées</option>
                <option value="urgent">Urgentes (&lt; 7 jours)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content */}
          <div className="lg:w-2/3">
        {/* Results header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Résultats de recherche</h2>
            <p className="text-gray-600">
              {!loading && !error && `${pagination.total} bourses trouvées`}
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <select
              className="rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm"
            >
              <option value="recent">Plus récentes</option>
              <option value="deadline">Date limite proche</option>
              <option value="relevance">Pertinence</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-16">
            <Spin size="large" tip="Chargement des bourses..." />
          </div>
        ) : error ? (
          <Alert
            message="Erreur"
            description={error}
            type="error"
            showIcon
            className="mb-6 rounded-xl shadow-md"
          />
        ) : (
          <>
            {/* Mobile Ad - Only visible on small screens */}
            <div className="mb-8 md:hidden">
              <AdPlacement
                adSlot="2345678901"
                adSize="rectangle"
                responsive={true}
                fullWidth={true}
              />
            </div>

            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {scholarships.map((scholarship, index) => (
                <div key={scholarship.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                  <EnhancedScholarshipCard
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    level={scholarship.level}
                    country={scholarship.country}
                    onClick={(id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`}
                    index={index}
                  />
                </div>
              ))}
            </div>

            {/* No Results Message */}
            {scholarships.length === 0 && (
              <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune bourse trouvée</h3>
                <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                  Essayez d'ajuster vos critères de recherche ou de filtrage pour trouver ce que vous cherchez.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedLevel('');
                    setSelectedCountry('');
                    setPagination(prev => ({ ...prev, page: 1 }));
                  }}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Réinitialiser les filtres
                </button>
              </div>
            )}

            {/* Pagination */}
            {pagination.total > 0 && (
              <div className="flex justify-center mt-12">
                <Pagination
                  current={pagination.page}
                  total={pagination.total}
                  pageSize={pagination.limit}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total) => `Total ${total} bourses`}
                  className="shadow-sm rounded-xl p-2 bg-white"
                />
              </div>
            )}
          </>
        )}
            </div>

            {/* Sidebar */}
            <ProfessionalSidebar
              config={{
                type: 'levels' as const,
                currentItem: selectedLevel || undefined,
                limit: 10
              }}
              className="lg:w-1/3"
            />
          </div>
        </div>

        {/* Page End Suggestions */}
        <PageEndSuggestions
          currentPageType="scholarship"
          currentItem={selectedLevel || selectedCountry}
        />
      </div>
    );
};

export default Scholarships;