[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx": "154", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx": "155", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx": "156", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx": "157", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx": "158", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx": "159", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts": "160", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx": "161", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx": "162", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx": "163", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx": "164", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx": "165", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx": "166", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts": "167", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx": "168", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx": "169", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts": "170", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx": "171", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx": "172", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/PageEndSuggestions.tsx": "173", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdPlacement.tsx": "174", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardLevelPage.tsx": "175", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SimplifiedSidebar.tsx": "176", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardCountryPage.tsx": "177", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/filters/StandardizedFilters.tsx": "178"}, {"size": 274, "mtime": 1745947759865, "results": "179", "hashOfConfig": "180"}, {"size": 2118, "mtime": 1745983514069, "results": "181", "hashOfConfig": "180"}, {"size": 5438, "mtime": 1745977740794, "results": "182", "hashOfConfig": "180"}, {"size": 10242, "mtime": 1745981563339, "results": "183", "hashOfConfig": "180"}, {"size": 1742, "mtime": 1745977713025, "results": "184", "hashOfConfig": "180"}, {"size": 7131, "mtime": 1745981562258, "results": "185", "hashOfConfig": "180"}, {"size": 10498, "mtime": 1745982258490, "results": "186", "hashOfConfig": "180"}, {"size": 3076, "mtime": 1745945973317, "results": "187", "hashOfConfig": "180"}, {"size": 553, "mtime": 1745978322072, "results": "188", "hashOfConfig": "180"}, {"size": 6250, "mtime": 1745977773426, "results": "189", "hashOfConfig": "180"}, {"size": 2446, "mtime": 1745945132621, "results": "190", "hashOfConfig": "180"}, {"size": 2645, "mtime": 1745977581979, "results": "191", "hashOfConfig": "180"}, {"size": 4167, "mtime": 1745981843303, "results": "192", "hashOfConfig": "180"}, {"size": 5713, "mtime": 1745981758114, "results": "193", "hashOfConfig": "180"}, {"size": 675, "mtime": 1745976791748, "results": "194", "hashOfConfig": "180"}, {"size": 1059, "mtime": 1745976720607, "results": "195", "hashOfConfig": "180"}, {"size": 3452, "mtime": 1745946003719, "results": "196", "hashOfConfig": "180"}, {"size": 2518, "mtime": 1745983866923, "results": "197", "hashOfConfig": "180"}, {"size": 1737, "mtime": 1745978376608, "results": "198", "hashOfConfig": "180"}, {"size": 4075, "mtime": 1745982269507, "results": "199", "hashOfConfig": "180"}, {"size": 4531, "mtime": 1745982263875, "results": "200", "hashOfConfig": "180"}, {"size": 5406, "mtime": 1745982274929, "results": "201", "hashOfConfig": "180"}, {"size": 2535, "mtime": 1745978386143, "results": "202", "hashOfConfig": "180"}, {"size": 737, "mtime": 1745944438688, "results": "203", "hashOfConfig": "180"}, {"size": 2323, "mtime": 1745982233889, "results": "204", "hashOfConfig": "180"}, {"size": 274, "mtime": 1745947759865, "results": "205", "hashOfConfig": "206"}, {"size": 4324, "mtime": 1746276088446, "results": "207", "hashOfConfig": "206"}, {"size": 7131, "mtime": 1745981562258, "results": "208", "hashOfConfig": "206"}, {"size": 10498, "mtime": 1745982258490, "results": "209", "hashOfConfig": "206"}, {"size": 10242, "mtime": 1745981563339, "results": "210", "hashOfConfig": "206"}, {"size": 1742, "mtime": 1745977713025, "results": "211", "hashOfConfig": "206"}, {"size": 8315, "mtime": 1746204095547, "results": "212", "hashOfConfig": "206"}, {"size": 3076, "mtime": 1745945973317, "results": "213", "hashOfConfig": "206"}, {"size": 2535, "mtime": 1746033992389, "results": "214", "hashOfConfig": "206"}, {"size": 1737, "mtime": 1745978376608, "results": "215", "hashOfConfig": "206"}, {"size": 6250, "mtime": 1745977773426, "results": "216", "hashOfConfig": "206"}, {"size": 553, "mtime": 1745978322072, "results": "217", "hashOfConfig": "206"}, {"size": 2446, "mtime": 1745945132621, "results": "218", "hashOfConfig": "206"}, {"size": 5154, "mtime": 1746276100698, "results": "219", "hashOfConfig": "206"}, {"size": 8097, "mtime": 1745983506688, "results": "220", "hashOfConfig": "206"}, {"size": 6024, "mtime": 1746026061709, "results": "221", "hashOfConfig": "206"}, {"size": 9559, "mtime": 1746270350218, "results": "222", "hashOfConfig": "206"}, {"size": 737, "mtime": 1745944438688, "results": "223", "hashOfConfig": "206"}, {"size": 2645, "mtime": 1745977581979, "results": "224", "hashOfConfig": "206"}, {"size": 4531, "mtime": 1745982263875, "results": "225", "hashOfConfig": "206"}, {"size": 4075, "mtime": 1745982269507, "results": "226", "hashOfConfig": "206"}, {"size": 5406, "mtime": 1745982274929, "results": "227", "hashOfConfig": "206"}, {"size": 5713, "mtime": 1745981758114, "results": "228", "hashOfConfig": "206"}, {"size": 675, "mtime": 1745976791748, "results": "229", "hashOfConfig": "206"}, {"size": 4167, "mtime": 1745981843303, "results": "230", "hashOfConfig": "206"}, {"size": 2115, "mtime": 1746029576846, "results": "231", "hashOfConfig": "206"}, {"size": 1689, "mtime": 1745982730905, "results": "232", "hashOfConfig": "206"}, {"size": 19288, "mtime": 1745983400384, "results": "233", "hashOfConfig": "206"}, {"size": 1059, "mtime": 1745976720607, "results": "234", "hashOfConfig": "206"}, {"size": 2535, "mtime": 1745978386143, "results": "235", "hashOfConfig": "206"}, {"size": 14052, "mtime": 1746274142489, "results": "236", "hashOfConfig": "206"}, {"size": 3939, "mtime": 1746017528736, "results": "237", "hashOfConfig": "206"}, {"size": 13857, "mtime": 1746282401482, "results": "238", "hashOfConfig": "206"}, {"size": 11940, "mtime": 1746252382064, "results": "239", "hashOfConfig": "206"}, {"size": 38877, "mtime": 1746252407519, "results": "240", "hashOfConfig": "206"}, {"size": 8393, "mtime": 1746249939564, "results": "241", "hashOfConfig": "206"}, {"size": 11544, "mtime": 1746272209267, "results": "242", "hashOfConfig": "206"}, {"size": 1343, "mtime": 1746033436995, "results": "243", "hashOfConfig": "206"}, {"size": 1752, "mtime": 1746274101102, "results": "244", "hashOfConfig": "206"}, {"size": 3341, "mtime": 1746199132190, "results": "245", "hashOfConfig": "206"}, {"size": 7045, "mtime": 1746199160974, "results": "246", "hashOfConfig": "206"}, {"size": 4103, "mtime": 1746200520123, "results": "247", "hashOfConfig": "206"}, {"size": 6276, "mtime": 1746249196201, "results": "248", "hashOfConfig": "206"}, {"size": 7222, "mtime": 1746249216418, "results": "249", "hashOfConfig": "206"}, {"size": 1105, "mtime": 1746201832350, "results": "250", "hashOfConfig": "206"}, {"size": 921, "mtime": 1746202207790, "results": "251", "hashOfConfig": "206"}, {"size": 11379, "mtime": 1746276323665, "results": "252", "hashOfConfig": "206"}, {"size": 1059, "mtime": 1746226321253, "results": "253", "hashOfConfig": "206"}, {"size": 7914, "mtime": 1746251582912, "results": "254", "hashOfConfig": "206"}, {"size": 4784, "mtime": 1746252717773, "results": "255", "hashOfConfig": "206"}, {"size": 1777, "mtime": 1746254015165, "results": "256", "hashOfConfig": "206"}, {"size": 9435, "mtime": 1746252638103, "results": "257", "hashOfConfig": "206"}, {"size": 5504, "mtime": 1746275135511, "results": "258", "hashOfConfig": "206"}, {"size": 1053, "mtime": 1752353080389, "results": "259", "hashOfConfig": "260"}, {"size": 6124, "mtime": 1752567826307, "results": "261", "hashOfConfig": "260"}, {"size": 13137, "mtime": 1752575490167, "results": "262", "hashOfConfig": "260"}, {"size": 19195, "mtime": 1747280789154, "results": "263", "hashOfConfig": "260"}, {"size": 3587, "mtime": 1747235972243, "results": "264", "hashOfConfig": "260"}, {"size": 10653, "mtime": 1747235897205, "results": "265", "hashOfConfig": "260"}, {"size": 3076, "mtime": 1745945973317, "results": "266", "hashOfConfig": "260"}, {"size": 1974, "mtime": 1747278879917, "results": "267", "hashOfConfig": "260"}, {"size": 577, "mtime": 1752352136699, "results": "268", "hashOfConfig": "260"}, {"size": 18635, "mtime": 1747235596822, "results": "269", "hashOfConfig": "270"}, {"size": 2446, "mtime": 1745945132621, "results": "271", "hashOfConfig": "260"}, {"size": 19874, "mtime": 1752251800113, "results": "272", "hashOfConfig": "260"}, {"size": 5624, "mtime": 1752347411216, "results": "273", "hashOfConfig": "260"}, {"size": 11326, "mtime": 1752283692783, "results": "274", "hashOfConfig": "260"}, {"size": 14078, "mtime": 1752283590200, "results": "275", "hashOfConfig": "260"}, {"size": 12834, "mtime": 1752553805022, "results": "276", "hashOfConfig": "260"}, {"size": 7045, "mtime": 1746199160974, "results": "277", "hashOfConfig": "260"}, {"size": 3341, "mtime": 1746199132190, "results": "278", "hashOfConfig": "260"}, {"size": 1059, "mtime": 1746226321253, "results": "279", "hashOfConfig": "260"}, {"size": 6276, "mtime": 1746249196201, "results": "280", "hashOfConfig": "260"}, {"size": 921, "mtime": 1746202207790, "results": "281", "hashOfConfig": "260"}, {"size": 1695, "mtime": 1747186871230, "results": "282", "hashOfConfig": "270"}, {"size": 3161, "mtime": 1747232764014, "results": "283", "hashOfConfig": "270"}, {"size": 7071, "mtime": 1752353425661, "results": "284", "hashOfConfig": "260"}, {"size": 12973, "mtime": 1747224498475, "results": "285", "hashOfConfig": "260"}, {"size": 6490, "mtime": 1752353387940, "results": "286", "hashOfConfig": "260"}, {"size": 8394, "mtime": 1752353443428, "results": "287", "hashOfConfig": "260"}, {"size": 8023, "mtime": 1752346082272, "results": "288", "hashOfConfig": "260"}, {"size": 675, "mtime": 1745976791748, "results": "289", "hashOfConfig": "270"}, {"size": 7860, "mtime": 1752251788545, "results": "290", "hashOfConfig": "260"}, {"size": 7222, "mtime": 1746249216418, "results": "291", "hashOfConfig": "260"}, {"size": 9917, "mtime": 1752283723085, "results": "292", "hashOfConfig": "260"}, {"size": 5948, "mtime": 1752352806967, "results": "293", "hashOfConfig": "260"}, {"size": 2535, "mtime": 1745978386143, "results": "294", "hashOfConfig": "260"}, {"size": 3877, "mtime": 1747235933700, "results": "295", "hashOfConfig": "260"}, {"size": 1689, "mtime": 1745982730905, "results": "296", "hashOfConfig": "260"}, {"size": 3697, "mtime": 1747184461868, "results": "297", "hashOfConfig": "270"}, {"size": 959, "mtime": 1747186815101, "results": "298", "hashOfConfig": "270"}, {"size": 5906, "mtime": 1752553992443, "results": "299", "hashOfConfig": "260"}, {"size": 2847, "mtime": 1747187027857, "results": "300", "hashOfConfig": "260"}, {"size": 2604, "mtime": 1747279467729, "results": "301", "hashOfConfig": "260"}, {"size": 8119, "mtime": 1747220020952, "results": "302", "hashOfConfig": "270"}, {"size": 8243, "mtime": 1747220059414, "results": "303", "hashOfConfig": "270"}, {"size": 3071, "mtime": 1747221577347, "results": "304", "hashOfConfig": "270"}, {"size": 6125, "mtime": 1747221750779, "results": "305", "hashOfConfig": "270"}, {"size": 6017, "mtime": 1747221715802, "results": "306", "hashOfConfig": "270"}, {"size": 3890, "mtime": 1747221780672, "results": "307", "hashOfConfig": "270"}, {"size": 3377, "mtime": 1747221613654, "results": "308", "hashOfConfig": "270"}, {"size": 3156, "mtime": 1747221640258, "results": "309", "hashOfConfig": "270"}, {"size": 7752, "mtime": 1747237735157, "results": "310", "hashOfConfig": "260"}, {"size": 9090, "mtime": 1752553387020, "results": "311", "hashOfConfig": "260"}, {"size": 9820, "mtime": 1747279005156, "results": "312", "hashOfConfig": "260"}, {"size": 7977, "mtime": 1752360488635, "results": "313", "hashOfConfig": "260"}, {"size": 6653, "mtime": 1752553430152, "results": "314", "hashOfConfig": "260"}, {"size": 9620, "mtime": 1747242054549, "results": "315", "hashOfConfig": "260"}, {"size": 9567, "mtime": 1747242099457, "results": "316", "hashOfConfig": "260"}, {"size": 5262, "mtime": 1747242002891, "results": "317", "hashOfConfig": "260"}, {"size": 7534, "mtime": 1752351193762, "results": "318", "hashOfConfig": "260"}, {"size": 28904, "mtime": 1752248907873, "results": "319", "hashOfConfig": "260"}, {"size": 1536, "mtime": 1747237627552, "results": "320", "hashOfConfig": "260"}, {"size": 2783, "mtime": 1747275467037, "results": "321", "hashOfConfig": "260"}, {"size": 4730, "mtime": 1747275582856, "results": "322", "hashOfConfig": "260"}, {"size": 5097, "mtime": 1747302359006, "results": "323", "hashOfConfig": "270"}, {"size": 1008, "mtime": 1752283918060, "results": "324", "hashOfConfig": "260"}, {"size": 9653, "mtime": 1752283637791, "results": "325", "hashOfConfig": "260"}, {"size": 358, "mtime": 1752254238558, "results": "326", "hashOfConfig": "260"}, {"size": 3337, "mtime": 1752554200802, "results": "327", "hashOfConfig": "260"}, {"size": 1433, "mtime": 1752283753270, "results": "328", "hashOfConfig": "260"}, {"size": 3435, "mtime": 1752283114606, "results": "329", "hashOfConfig": "260"}, {"size": 4212, "mtime": 1752553908165, "results": "330", "hashOfConfig": "260"}, {"size": 1083, "mtime": 1752283244823, "results": "331", "hashOfConfig": "260"}, {"size": 5685, "mtime": 1752287194809, "results": "332", "hashOfConfig": "260"}, {"size": 16723, "mtime": 1752348343896, "results": "333", "hashOfConfig": "260"}, {"size": 14378, "mtime": 1752304560424, "results": "334", "hashOfConfig": "260"}, {"size": 16323, "mtime": 1747156326467, "results": "335", "hashOfConfig": "260"}, {"size": 10650, "mtime": 1752557935947, "results": "336", "hashOfConfig": "260"}, {"size": 11030, "mtime": 1752346737219, "results": "337", "hashOfConfig": "260"}, {"size": 15352, "mtime": 1752346794610, "results": "338", "hashOfConfig": "260"}, {"size": 2511, "mtime": 1752574238553, "results": "339", "hashOfConfig": "260"}, {"size": 10639, "mtime": 1752347902146, "results": "340", "hashOfConfig": "260"}, {"size": 14557, "mtime": 1752347930663, "results": "341", "hashOfConfig": "260"}, {"size": 12835, "mtime": 1752351140445, "results": "342", "hashOfConfig": "260"}, {"size": 9844, "mtime": 1752354895907, "results": "343", "hashOfConfig": "260"}, {"size": 6698, "mtime": 1752571190306, "results": "344", "hashOfConfig": "260"}, {"size": 7170, "mtime": 1752353841725, "results": "345", "hashOfConfig": "260"}, {"size": 7609, "mtime": 1752571252778, "results": "346", "hashOfConfig": "260"}, {"size": 4433, "mtime": 1752389001452, "results": "347", "hashOfConfig": "260"}, {"size": 18226, "mtime": 1752381116505, "results": "348", "hashOfConfig": "260"}, {"size": 11905, "mtime": 1752554305331, "results": "349", "hashOfConfig": "260"}, {"size": 12528, "mtime": 1752388836464, "results": "350", "hashOfConfig": "260"}, {"size": 15557, "mtime": 1752388873535, "results": "351", "hashOfConfig": "260"}, {"size": 6616, "mtime": 1752388753535, "results": "352", "hashOfConfig": "260"}, {"size": 1674, "mtime": 1752572993040, "results": "353", "hashOfConfig": "260"}, {"size": 1565, "mtime": 1752573341488, "results": "354", "hashOfConfig": "260"}, {"size": 8437, "mtime": 1752557724020, "results": "355", "hashOfConfig": "260"}, {"size": 3756, "mtime": 1752565617212, "results": "356", "hashOfConfig": "260"}, {"size": 11173, "mtime": 1752573951220, "results": "357", "hashOfConfig": "260"}, {"size": 5285, "mtime": 1752573894027, "results": "358", "hashOfConfig": "260"}, {"size": 11061, "mtime": 1752574097974, "results": "359", "hashOfConfig": "260"}, {"size": 8218, "mtime": 1752575098604, "results": "360", "hashOfConfig": "260"}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["895"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["896", "897", "898"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["899"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["900"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["901", "902"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["903", "904", "905"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["906"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["907", "908"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["909", "910", "911"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["912"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["913"], ["914"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["915"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["916"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["917"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["918"], ["919"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["920"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["921", "922"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["923", "924", "925"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["926", "927"], ["928"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["929", "930"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["931", "932", "933", "934"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", [], ["935"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["936"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", ["937"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["938"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["939"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["940"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["941", "942"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["943", "944"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["945"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["946"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["947"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["948", "949", "950"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx", ["951"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx", ["952"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx", ["953"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx", ["954"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx", ["955", "956"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx", ["957"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx", ["958", "959"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx", ["960"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/PageEndSuggestions.tsx", ["961", "962"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdPlacement.tsx", ["963"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardLevelPage.tsx", ["964", "965"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SimplifiedSidebar.tsx", ["966"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardCountryPage.tsx", ["967", "968"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/filters/StandardizedFilters.tsx", [], [], {"ruleId": "969", "severity": 1, "message": "970", "line": 170, "column": 7, "nodeType": "971", "messageId": "972", "endLine": 170, "endColumn": 15}, {"ruleId": "973", "severity": 1, "message": "974", "line": 51, "column": 15, "nodeType": "975", "endLine": 51, "endColumn": 74}, {"ruleId": "973", "severity": 1, "message": "974", "line": 57, "column": 15, "nodeType": "975", "endLine": 57, "endColumn": 74}, {"ruleId": "973", "severity": 1, "message": "974", "line": 63, "column": 15, "nodeType": "975", "endLine": 63, "endColumn": 74}, {"ruleId": "969", "severity": 1, "message": "970", "line": 170, "column": 7, "nodeType": "971", "messageId": "972", "endLine": 170, "endColumn": 15}, {"ruleId": "976", "severity": 1, "message": "977", "line": 44, "column": 6, "nodeType": "978", "endLine": 44, "endColumn": 68, "suggestions": "979"}, {"ruleId": "969", "severity": 1, "message": "980", "line": 3, "column": 8, "nodeType": "971", "messageId": "972", "endLine": 3, "endColumn": 19}, {"ruleId": "969", "severity": 1, "message": "981", "line": 31, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 31, "endColumn": 23}, {"ruleId": "973", "severity": 1, "message": "974", "line": 51, "column": 15, "nodeType": "975", "endLine": 51, "endColumn": 74}, {"ruleId": "973", "severity": 1, "message": "974", "line": 57, "column": 15, "nodeType": "975", "endLine": 57, "endColumn": 74}, {"ruleId": "973", "severity": 1, "message": "974", "line": 63, "column": 15, "nodeType": "975", "endLine": 63, "endColumn": 74}, {"ruleId": "969", "severity": 1, "message": "981", "line": 34, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 34, "endColumn": 23}, {"ruleId": "969", "severity": 1, "message": "982", "line": 10, "column": 3, "nodeType": "971", "messageId": "972", "endLine": 10, "endColumn": 8}, {"ruleId": "969", "severity": 1, "message": "983", "line": 15, "column": 24, "nodeType": "971", "messageId": "972", "endLine": 15, "endColumn": 43}, {"ruleId": "969", "severity": 1, "message": "984", "line": 3, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 3, "endColumn": 21}, {"ruleId": "969", "severity": 1, "message": "985", "line": 44, "column": 9, "nodeType": "971", "messageId": "972", "endLine": 44, "endColumn": 17}, {"ruleId": "986", "severity": 1, "message": "987", "line": 162, "column": 36, "nodeType": "988", "messageId": "989", "endLine": 162, "endColumn": 60}, {"ruleId": "976", "severity": 1, "message": "990", "line": 37, "column": 6, "nodeType": "978", "endLine": 37, "endColumn": 42, "suggestions": "991", "suppressions": "992"}, {"ruleId": "969", "severity": 1, "message": "993", "line": 236, "column": 9, "nodeType": "971", "messageId": "972", "endLine": 236, "endColumn": 30}, {"ruleId": "976", "severity": 1, "message": "990", "line": 64, "column": 6, "nodeType": "978", "endLine": 64, "endColumn": 61, "suggestions": "994", "suppressions": "995"}, {"ruleId": "976", "severity": 1, "message": "990", "line": 29, "column": 6, "nodeType": "978", "endLine": 29, "endColumn": 31, "suggestions": "996", "suppressions": "997"}, {"ruleId": "976", "severity": 1, "message": "998", "line": 25, "column": 6, "nodeType": "978", "endLine": 25, "endColumn": 8, "suggestions": "999", "suppressions": "1000"}, {"ruleId": "976", "severity": 1, "message": "1001", "line": 177, "column": 6, "nodeType": "978", "endLine": 177, "endColumn": 8, "suggestions": "1002", "suppressions": "1003"}, {"ruleId": "969", "severity": 1, "message": "984", "line": 3, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 3, "endColumn": 21}, {"ruleId": "976", "severity": 1, "message": "1004", "line": 37, "column": 6, "nodeType": "978", "endLine": 37, "endColumn": 8, "suggestions": "1005", "suppressions": "1006"}, {"ruleId": "976", "severity": 1, "message": "977", "line": 64, "column": 6, "nodeType": "978", "endLine": 64, "endColumn": 68, "suggestions": "1007"}, {"ruleId": "973", "severity": 1, "message": "974", "line": 135, "column": 21, "nodeType": "975", "endLine": 135, "endColumn": 96}, {"ruleId": "973", "severity": 1, "message": "974", "line": 141, "column": 21, "nodeType": "975", "endLine": 141, "endColumn": 96}, {"ruleId": "973", "severity": 1, "message": "974", "line": 108, "column": 21, "nodeType": "975", "endLine": 108, "endColumn": 94}, {"ruleId": "973", "severity": 1, "message": "974", "line": 114, "column": 21, "nodeType": "975", "endLine": 114, "endColumn": 94}, {"ruleId": "973", "severity": 1, "message": "974", "line": 120, "column": 21, "nodeType": "975", "endLine": 120, "endColumn": 94}, {"ruleId": "969", "severity": 1, "message": "1008", "line": 2, "column": 105, "nodeType": "971", "messageId": "972", "endLine": 2, "endColumn": 111}, {"ruleId": "969", "severity": 1, "message": "1009", "line": 3, "column": 111, "nodeType": "971", "messageId": "972", "endLine": 3, "endColumn": 126}, {"ruleId": "976", "severity": 1, "message": "990", "line": 45, "column": 6, "nodeType": "978", "endLine": 45, "endColumn": 31, "suggestions": "1010", "suppressions": "1011"}, {"ruleId": "969", "severity": 1, "message": "985", "line": 44, "column": 9, "nodeType": "971", "messageId": "972", "endLine": 44, "endColumn": 17}, {"ruleId": "976", "severity": 1, "message": "1012", "line": 106, "column": 6, "nodeType": "978", "endLine": 106, "endColumn": 8, "suggestions": "1013"}, {"ruleId": "973", "severity": 1, "message": "974", "line": 117, "column": 17, "nodeType": "975", "endLine": 117, "endColumn": 131}, {"ruleId": "973", "severity": 1, "message": "974", "line": 125, "column": 17, "nodeType": "975", "endLine": 125, "endColumn": 131}, {"ruleId": "973", "severity": 1, "message": "974", "line": 133, "column": 17, "nodeType": "975", "endLine": 133, "endColumn": 131}, {"ruleId": "973", "severity": 1, "message": "974", "line": 141, "column": 17, "nodeType": "975", "endLine": 141, "endColumn": 131}, {"ruleId": "976", "severity": 1, "message": "1004", "line": 37, "column": 6, "nodeType": "978", "endLine": 37, "endColumn": 8, "suggestions": "1014", "suppressions": "1015"}, {"ruleId": "976", "severity": 1, "message": "998", "line": 25, "column": 6, "nodeType": "978", "endLine": 25, "endColumn": 8, "suggestions": "1016", "suppressions": "1017"}, {"ruleId": "969", "severity": 1, "message": "1018", "line": 38, "column": 9, "nodeType": "971", "messageId": "972", "endLine": 38, "endColumn": 12}, {"ruleId": "973", "severity": 1, "message": "974", "line": 101, "column": 26, "nodeType": "975", "endLine": 101, "endColumn": 87}, {"ruleId": "969", "severity": 1, "message": "1019", "line": 2, "column": 40, "nodeType": "971", "messageId": "972", "endLine": 2, "endColumn": 44}, {"ruleId": "976", "severity": 1, "message": "1020", "line": 38, "column": 6, "nodeType": "978", "endLine": 38, "endColumn": 8, "suggestions": "1021"}, {"ruleId": "973", "severity": 1, "message": "974", "line": 186, "column": 21, "nodeType": "975", "endLine": 186, "endColumn": 74}, {"ruleId": "973", "severity": 1, "message": "974", "line": 188, "column": 21, "nodeType": "975", "endLine": 188, "endColumn": 74}, {"ruleId": "969", "severity": 1, "message": "1019", "line": 4, "column": 30, "nodeType": "971", "messageId": "972", "endLine": 4, "endColumn": 34}, {"ruleId": "969", "severity": 1, "message": "1022", "line": 7, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 7, "endColumn": 16}, {"ruleId": "1023", "severity": 1, "message": "1024", "line": 172, "column": 1, "nodeType": "1025", "endLine": 179, "endColumn": 3}, {"ruleId": "969", "severity": 1, "message": "1022", "line": 2, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 2, "endColumn": 16}, {"ruleId": "969", "severity": 1, "message": "1026", "line": 5, "column": 16, "nodeType": "971", "messageId": "972", "endLine": 5, "endColumn": 20}, {"ruleId": "969", "severity": 1, "message": "1027", "line": 13, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 13, "endColumn": 20}, {"ruleId": "969", "severity": 1, "message": "1028", "line": 15, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 15, "endColumn": 21}, {"ruleId": "976", "severity": 1, "message": "1029", "line": 106, "column": 6, "nodeType": "978", "endLine": 106, "endColumn": 8, "suggestions": "1030"}, {"ruleId": "969", "severity": 1, "message": "1031", "line": 10, "column": 3, "nodeType": "971", "messageId": "972", "endLine": 10, "endColumn": 12}, {"ruleId": "969", "severity": 1, "message": "981", "line": 32, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 32, "endColumn": 23}, {"ruleId": "976", "severity": 1, "message": "1032", "line": 26, "column": 6, "nodeType": "978", "endLine": 26, "endColumn": 24, "suggestions": "1033"}, {"ruleId": "976", "severity": 1, "message": "1034", "line": 35, "column": 6, "nodeType": "978", "endLine": 35, "endColumn": 15, "suggestions": "1035"}, {"ruleId": "976", "severity": 1, "message": "1036", "line": 86, "column": 6, "nodeType": "978", "endLine": 86, "endColumn": 14, "suggestions": "1037"}, {"ruleId": "976", "severity": 1, "message": "1036", "line": 98, "column": 6, "nodeType": "978", "endLine": 98, "endColumn": 14, "suggestions": "1038"}, {"ruleId": "969", "severity": 1, "message": "1039", "line": 3, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 3, "endColumn": 21}, {"ruleId": "969", "severity": 1, "message": "981", "line": 31, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 31, "endColumn": 23}, {"ruleId": "976", "severity": 1, "message": "1040", "line": 49, "column": 6, "nodeType": "978", "endLine": 49, "endColumn": 32, "suggestions": "1041"}, {"ruleId": "969", "severity": 1, "message": "1039", "line": 3, "column": 10, "nodeType": "971", "messageId": "972", "endLine": 3, "endColumn": 21}, {"ruleId": "969", "severity": 1, "message": "981", "line": 31, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 31, "endColumn": 23}, {"ruleId": "976", "severity": 1, "message": "1042", "line": 37, "column": 6, "nodeType": "978", "endLine": 37, "endColumn": 47, "suggestions": "1043"}, {"ruleId": "969", "severity": 1, "message": "1044", "line": 59, "column": 9, "nodeType": "971", "messageId": "972", "endLine": 59, "endColumn": 21}, {"ruleId": "969", "severity": 1, "message": "981", "line": 49, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 49, "endColumn": 23}, {"ruleId": "976", "severity": 1, "message": "977", "line": 113, "column": 6, "nodeType": "978", "endLine": 113, "endColumn": 37, "suggestions": "1045"}, {"ruleId": "976", "severity": 1, "message": "977", "line": 55, "column": 6, "nodeType": "978", "endLine": 55, "endColumn": 40, "suggestions": "1046"}, {"ruleId": "969", "severity": 1, "message": "981", "line": 50, "column": 11, "nodeType": "971", "messageId": "972", "endLine": 50, "endColumn": 23}, {"ruleId": "976", "severity": 1, "message": "977", "line": 114, "column": 6, "nodeType": "978", "endLine": 114, "endColumn": 39, "suggestions": "1047"}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["1048"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1049"], ["1050"], "'sendEmailNotification' is assigned a value but never used.", ["1051"], ["1052"], ["1053"], ["1054"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["1055"], ["1056"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1057"], ["1058"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["1059"], ["1060"], ["1061"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["1062"], ["1063"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["1064"], ["1065"], ["1066"], ["1067"], ["1068"], "'api' is assigned a value but never used.", "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["1069"], "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Text' is assigned a value but never used.", "'DateFormat' is defined but never used.", "'ApiResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["1070"], "'XMarkIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGuides'. Either include it or remove the dependency array.", ["1071"], "React Hook useEffect has a missing dependency: 'fetchOpportunities'. Either include it or remove the dependency array.", ["1072"], "React Hook useEffect has a missing dependency: 'closeDropdown'. Either include it or remove the dependency array.", ["1073"], ["1074"], "'useLanguage' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchOpportunitiesByType' and 'fetchTypeStatistics'. Either include them or remove the dependency array.", ["1075"], "React Hook useEffect has a missing dependency: 'fetchSuggestions'. Either include it or remove the dependency array.", ["1076"], "'adSizeConfig' is assigned a value but never used.", ["1077"], ["1078"], ["1079"], {"desc": "1080", "fix": "1081"}, {"desc": "1082", "fix": "1083"}, {"kind": "1084", "justification": "1085"}, {"desc": "1086", "fix": "1087"}, {"kind": "1084", "justification": "1085"}, {"desc": "1088", "fix": "1089"}, {"kind": "1084", "justification": "1085"}, {"desc": "1090", "fix": "1091"}, {"kind": "1084", "justification": "1085"}, {"desc": "1092", "fix": "1093"}, {"kind": "1084", "justification": "1085"}, {"desc": "1094", "fix": "1095"}, {"kind": "1084", "justification": "1085"}, {"desc": "1080", "fix": "1096"}, {"desc": "1088", "fix": "1097"}, {"kind": "1084", "justification": "1085"}, {"desc": "1098", "fix": "1099"}, {"desc": "1094", "fix": "1100"}, {"kind": "1084", "justification": "1085"}, {"desc": "1090", "fix": "1101"}, {"kind": "1084", "justification": "1085"}, {"desc": "1102", "fix": "1103"}, {"desc": "1104", "fix": "1105"}, {"desc": "1106", "fix": "1107"}, {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"desc": "1110", "fix": "1112"}, {"desc": "1113", "fix": "1114"}, {"desc": "1115", "fix": "1116"}, {"desc": "1117", "fix": "1118"}, {"desc": "1119", "fix": "1120"}, {"desc": "1121", "fix": "1122"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "1123", "text": "1124"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "1125", "text": "1126"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "1127", "text": "1128"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "1129", "text": "1130"}, "Update the dependencies array to be: [initializeSetup]", {"range": "1131", "text": "1132"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "1133", "text": "1134"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1135", "text": "1136"}, {"range": "1137", "text": "1124"}, {"range": "1138", "text": "1130"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "1139", "text": "1140"}, {"range": "1141", "text": "1136"}, {"range": "1142", "text": "1132"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "1143", "text": "1144"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "1145", "text": "1146"}, "Update the dependencies array to be: [fetchGuides, selectedCategory]", {"range": "1147", "text": "1148"}, "Update the dependencies array to be: [fetchOpportunities, filters]", {"range": "1149", "text": "1150"}, "Update the dependencies array to be: [closeDropdown, isOpen]", {"range": "1151", "text": "1152"}, {"range": "1153", "text": "1152"}, "Update the dependencies array to be: [decodedType, currentPage, fetchOpportunitiesByType, fetchTypeStatistics]", {"range": "1154", "text": "1155"}, "Update the dependencies array to be: [currentPageType, currentItem, excludeId, fetchSuggestions]", {"range": "1156", "text": "1157"}, "Update the dependencies array to be: [pagination.page, config.level, fetchScholarships]", {"range": "1158", "text": "1159"}, "Update the dependencies array to be: [config.currentItem, config.limit, fetchScholarships]", {"range": "1160", "text": "1161"}, "Update the dependencies array to be: [pagination.page, config.country, fetchScholarships]", {"range": "1162", "text": "1163"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1964, 2026], [1914, 1939], [3435, 3437], "[adminInfo, stats]", [1201, 1203], [969, 971], [1326, 1328], "[backgrounds.length]", [3118, 3120], "[fetchSecurityEvents]", [734, 752], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]", [934, 943], "[fetchOpportunities, filters]", [2324, 2332], "[closeDropdown, isOpen]", [2652, 2660], [1499, 1525], "[decodedType, currentPage, fetchOpportunitiesByType, fetchTypeStatistics]", [900, 941], "[currentPageType, currentItem, excludeId, fetchSuggestions]", [3114, 3145], "[pagination.page, config.level, fetchScholarships]", [1396, 1430], "[config.currentItem, config.limit, fetchScholarships]", [3147, 3180], "[pagination.page, config.country, fetchScholarships]"]