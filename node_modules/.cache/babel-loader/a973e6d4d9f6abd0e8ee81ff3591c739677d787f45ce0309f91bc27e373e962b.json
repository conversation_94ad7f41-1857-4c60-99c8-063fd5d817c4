{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport StandardCountryPage from '../components/StandardCountryPage';\n\n// Helper function to get country flag\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getCountryFlag = countryName => {\n  const flagMap = {\n    'France': '🇫🇷',\n    'Canada': '🇨🇦',\n    'États-Unis': '🇺🇸',\n    'Allemagne': '🇩🇪',\n    'Royaume-Uni': '🇬🇧',\n    'Australie': '🇦🇺',\n    'Suisse': '🇨🇭',\n    'Belgique': '🇧🇪',\n    'Pays-Bas': '🇳🇱',\n    'Suède': '🇸🇪',\n    'Norvège': '🇳🇴',\n    'Danemark': '🇩🇰',\n    'Finlande': '🇫🇮',\n    'Japon': '🇯🇵',\n    'Corée du Sud': '🇰🇷',\n    'Singapour': '🇸🇬',\n    'Nouvelle-Zélande': '🇳🇿',\n    'Italie': '🇮🇹',\n    'Espagne': '🇪🇸',\n    'Portugal': '🇵🇹'\n  };\n  return flagMap[countryName] || '🌍';\n};\nconst CountryDetail = () => {\n  _s();\n  const {\n    country\n  } = useParams();\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n  const pageConfig = {\n    country: decodedCountry,\n    title: `Bourses d'Études en ${decodedCountry} | Opportunités de Financement`,\n    description: `Découvrez toutes les bourses d'études disponibles en ${decodedCountry}. Financez vos études supérieures avec des opportunités de bourses prestigieuses.`,\n    keywords: `bourses ${decodedCountry}, financement études ${decodedCountry}, études supérieures ${decodedCountry}`,\n    heroTitle: 'Bourses d\\'Études en',\n    heroSubtitle: `Découvrez toutes les opportunités de bourses d'études disponibles en ${decodedCountry} et financez votre avenir académique.`,\n    infoTitle: `Pourquoi Étudier en ${decodedCountry} ?`,\n    infoContent: `${decodedCountry} offre un système éducatif de qualité mondiale avec des universités prestigieuses et des opportunités de recherche exceptionnelles. Avec une bourse d'études, vous pouvez accéder à cette excellence académique sans contraintes financières.`,\n    benefits: ['Universités de renommée mondiale', 'Programmes académiques d\\'excellence', 'Environnement multiculturel enrichissant', 'Opportunités de recherche avancée', 'Réseau professionnel international', 'Expérience culturelle unique'],\n    apiEndpoint: '/api/scholarships/search',\n    flag: getCountryFlag(decodedCountry)\n  };\n  return /*#__PURE__*/_jsxDEV(StandardCountryPage, {\n    config: pageConfig\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 10\n  }, this);\n};\n_s(CountryDetail, \"llnm9w59V8WVzyDr3L/T8v5CIos=\", false, function () {\n  return [useParams];\n});\n_c = CountryDetail;\nexport default CountryDetail;\nvar _c;\n$RefreshReg$(_c, \"CountryDetail\");", "map": {"version": 3, "names": ["React", "useParams", "StandardCountryPage", "jsxDEV", "_jsxDEV", "getCountryFlag", "countryName", "flagMap", "CountryDetail", "_s", "country", "decodedCountry", "decodeURIComponent", "pageConfig", "title", "description", "keywords", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "infoTitle", "infoContent", "benefits", "apiEndpoint", "flag", "config", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\nimport StandardCountryPage from '../components/StandardCountryPage';\n\n// Helper function to get country flag\nconst getCountryFlag = (countryName: string): string => {\n  const flagMap: { [key: string]: string } = {\n    'France': '🇫🇷',\n    'Canada': '🇨🇦',\n    'États-Unis': '🇺🇸',\n    'Allemagne': '🇩🇪',\n    'Royaume-Uni': '🇬🇧',\n    'Australie': '🇦🇺',\n    'Suisse': '🇨🇭',\n    'Belgique': '🇧🇪',\n    'Pays-Bas': '🇳🇱',\n    'Suède': '🇸🇪',\n    'Norvège': '🇳🇴',\n    'Danemark': '🇩🇰',\n    'Finlande': '🇫🇮',\n    'Japon': '🇯🇵',\n    'Corée du Sud': '🇰🇷',\n    'Singapour': '🇸🇬',\n    'Nouvelle-Zélande': '🇳🇿',\n    'Italie': '🇮🇹',\n    'Espagne': '🇪🇸',\n    'Portugal': '🇵🇹'\n  };\n  return flagMap[countryName] || '🌍';\n};\n\nconst CountryDetail: React.FC = () => {\n  const { country } = useParams<{ country: string }>();\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n\n  const pageConfig = {\n    country: decodedCountry,\n    title: `Bourses d'Études en ${decodedCountry} | Opportunités de Financement`,\n    description: `Découvrez toutes les bourses d'études disponibles en ${decodedCountry}. Financez vos études supérieures avec des opportunités de bourses prestigieuses.`,\n    keywords: `bourses ${decodedCountry}, financement études ${decodedCountry}, études supérieures ${decodedCountry}`,\n    heroTitle: 'Bourses d\\'Études en',\n    heroSubtitle: `Découvrez toutes les opportunités de bourses d'études disponibles en ${decodedCountry} et financez votre avenir académique.`,\n    infoTitle: `Pourquoi Étudier en ${decodedCountry} ?`,\n    infoContent: `${decodedCountry} offre un système éducatif de qualité mondiale avec des universités prestigieuses et des opportunités de recherche exceptionnelles. Avec une bourse d'études, vous pouvez accéder à cette excellence académique sans contraintes financières.`,\n    benefits: [\n      'Universités de renommée mondiale',\n      'Programmes académiques d\\'excellence',\n      'Environnement multiculturel enrichissant',\n      'Opportunités de recherche avancée',\n      'Réseau professionnel international',\n      'Expérience culturelle unique'\n    ],\n    apiEndpoint: '/api/scholarships/search',\n    flag: getCountryFlag(decodedCountry)\n  };\n\n  return <StandardCountryPage config={pageConfig} />;\n};\n\nexport default CountryDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,mBAAmB,MAAM,mCAAmC;;AAEnE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAIC,WAAmB,IAAa;EACtD,MAAMC,OAAkC,GAAG;IACzC,QAAQ,EAAE,MAAM;IAChB,QAAQ,EAAE,MAAM;IAChB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAM;IACf,SAAS,EAAE,MAAM;IACjB,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAM;IACf,cAAc,EAAE,MAAM;IACtB,WAAW,EAAE,MAAM;IACnB,kBAAkB,EAAE,MAAM;IAC1B,QAAQ,EAAE,MAAM;IAChB,SAAS,EAAE,MAAM;IACjB,UAAU,EAAE;EACd,CAAC;EACD,OAAOA,OAAO,CAACD,WAAW,CAAC,IAAI,IAAI;AACrC,CAAC;AAED,MAAME,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAQ,CAAC,GAAGT,SAAS,CAAsB,CAAC;EACpD,MAAMU,cAAc,GAAGD,OAAO,GAAGE,kBAAkB,CAACF,OAAO,CAAC,GAAG,EAAE;EAEjE,MAAMG,UAAU,GAAG;IACjBH,OAAO,EAAEC,cAAc;IACvBG,KAAK,EAAE,uBAAuBH,cAAc,gCAAgC;IAC5EI,WAAW,EAAE,wDAAwDJ,cAAc,mFAAmF;IACtKK,QAAQ,EAAE,WAAWL,cAAc,wBAAwBA,cAAc,wBAAwBA,cAAc,EAAE;IACjHM,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,wEAAwEP,cAAc,uCAAuC;IAC3IQ,SAAS,EAAE,uBAAuBR,cAAc,IAAI;IACpDS,WAAW,EAAE,GAAGT,cAAc,+OAA+O;IAC7QU,QAAQ,EAAE,CACR,kCAAkC,EAClC,sCAAsC,EACtC,0CAA0C,EAC1C,mCAAmC,EACnC,oCAAoC,EACpC,8BAA8B,CAC/B;IACDC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAElB,cAAc,CAACM,cAAc;EACrC,CAAC;EAED,oBAAOP,OAAA,CAACF,mBAAmB;IAACsB,MAAM,EAAEX;EAAW;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpD,CAAC;AAACnB,EAAA,CA1BID,aAAuB;EAAA,QACPP,SAAS;AAAA;AAAA4B,EAAA,GADzBrB,aAAuB;AA4B7B,eAAeA,aAAa;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}