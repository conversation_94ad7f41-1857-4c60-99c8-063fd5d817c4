{"ast": null, "code": "import { utcFormat } from \"./defaultLocale.js\";\nexport var isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\nvar formatIso = Date.prototype.toISOString ? formatIsoNative : utcFormat(isoSpecifier);\nexport default formatIso;", "map": {"version": 3, "names": ["utcFormat", "isoSpecifier", "formatIsoNative", "date", "toISOString", "formatIso", "Date", "prototype"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-time-format/src/isoFormat.js"], "sourcesContent": ["import {utcFormat} from \"./defaultLocale.js\";\n\nexport var isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\n\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\n\nvar formatIso = Date.prototype.toISOString\n    ? formatIsoNative\n    : utcFormat(isoSpecifier);\n\nexport default formatIso;\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,oBAAoB;AAE5C,OAAO,IAAIC,YAAY,GAAG,uBAAuB;AAEjD,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC;AAC3B;AAEA,IAAIC,SAAS,GAAGC,IAAI,CAACC,SAAS,CAACH,WAAW,GACpCF,eAAe,GACfF,SAAS,CAACC,YAAY,CAAC;AAE7B,eAAeI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}