{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nexport default function DefaultPanel(props) {\n  var _closable$closeIcon;\n  var prefixCls = props.prefixCls,\n    current = props.current,\n    total = props.total,\n    title = props.title,\n    description = props.description,\n    onClose = props.onClose,\n    onPrev = props.onPrev,\n    onNext = props.onNext,\n    onFinish = props.onFinish,\n    className = props.className,\n    closable = props.closable;\n  var ariaProps = pickAttrs(closable || {}, true);\n  var closeIcon = (_closable$closeIcon = closable === null || closable === void 0 ? void 0 : closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, \"\\xD7\");\n  var mergedClosable = !!closable;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className)\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, mergedClosable && /*#__PURE__*/React.createElement(\"button\", _extends({\n    type: \"button\",\n    onClick: onClose,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    className: \"\".concat(prefixCls, \"-close\")\n  }), closeIcon), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-header\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-description\")\n  }, description), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-sliders\")\n  }, total > 1 ? _toConsumableArray(Array.from({\n    length: total\n  }).keys()).map(function (item, index) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: item,\n      className: index === current ? 'active' : ''\n    });\n  }) : null), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-buttons\")\n  }, current !== 0 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-prev-btn\"),\n    onClick: onPrev\n  }, \"Prev\") : null, current === total - 1 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-finish-btn\"),\n    onClick: onFinish\n  }, \"Finish\") : /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-next-btn\"),\n    onClick: onNext\n  }, \"Next\")))));\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_extends", "React", "classNames", "pickAttrs", "DefaultPanel", "props", "_closable$closeIcon", "prefixCls", "current", "total", "title", "description", "onClose", "onPrev", "onNext", "onFinish", "className", "closable", "ariaProps", "closeIcon", "createElement", "concat", "mergedClosable", "type", "onClick", "Array", "from", "length", "keys", "map", "item", "index", "key"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/@rc-component/tour/es/TourStep/DefaultPanel.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nexport default function DefaultPanel(props) {\n  var _closable$closeIcon;\n  var prefixCls = props.prefixCls,\n    current = props.current,\n    total = props.total,\n    title = props.title,\n    description = props.description,\n    onClose = props.onClose,\n    onPrev = props.onPrev,\n    onNext = props.onNext,\n    onFinish = props.onFinish,\n    className = props.className,\n    closable = props.closable;\n  var ariaProps = pickAttrs(closable || {}, true);\n  var closeIcon = (_closable$closeIcon = closable === null || closable === void 0 ? void 0 : closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, \"\\xD7\");\n  var mergedClosable = !!closable;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className)\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, mergedClosable && /*#__PURE__*/React.createElement(\"button\", _extends({\n    type: \"button\",\n    onClick: onClose,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    className: \"\".concat(prefixCls, \"-close\")\n  }), closeIcon), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-header\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-description\")\n  }, description), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-sliders\")\n  }, total > 1 ? _toConsumableArray(Array.from({\n    length: total\n  }).keys()).map(function (item, index) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: item,\n      className: index === current ? 'active' : ''\n    });\n  }) : null), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-buttons\")\n  }, current !== 0 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-prev-btn\"),\n    onClick: onPrev\n  }, \"Prev\") : null, current === total - 1 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-finish-btn\"),\n    onClick: onFinish\n  }, \"Finish\") : /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-next-btn\"),\n    onClick: onNext\n  }, \"Next\")))));\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC1C,IAAIC,mBAAmB;EACvB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,MAAM,GAAGT,KAAK,CAACS,MAAM;IACrBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;EAC3B,IAAIC,SAAS,GAAGf,SAAS,CAACc,QAAQ,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;EAC/C,IAAIE,SAAS,GAAG,CAACb,mBAAmB,GAAGW,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,SAAS,MAAM,IAAI,IAAIb,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAG,aAAaL,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IACzNJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAE,MAAM,CAAC;EACV,IAAIe,cAAc,GAAG,CAAC,CAACL,QAAQ;EAC/B,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CJ,SAAS,EAAEd,UAAU,CAAC,EAAE,CAACmB,MAAM,CAACd,SAAS,EAAE,UAAU,CAAC,EAAES,SAAS;EACnE,CAAC,EAAE,aAAaf,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEe,cAAc,IAAI,aAAarB,KAAK,CAACmB,aAAa,CAAC,QAAQ,EAAEpB,QAAQ,CAAC;IACvEuB,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAEZ,OAAO;IAChB,YAAY,EAAE;EAChB,CAAC,EAAEM,SAAS,EAAE;IACZF,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,QAAQ;EAC1C,CAAC,CAAC,EAAEY,SAAS,CAAC,EAAE,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACtDJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,aAAaN,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEG,KAAK,CAAC,CAAC,EAAE,aAAaT,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAClDJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEI,WAAW,CAAC,EAAE,aAAaV,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACvDJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,aAAaN,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEE,KAAK,GAAG,CAAC,GAAGV,kBAAkB,CAAC0B,KAAK,CAACC,IAAI,CAAC;IAC3CC,MAAM,EAAElB;EACV,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACpC,OAAO,aAAa9B,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC9CY,GAAG,EAAEF,IAAI;MACTd,SAAS,EAAEe,KAAK,KAAKvB,OAAO,GAAG,QAAQ,GAAG;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,aAAaP,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAClDJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEC,OAAO,KAAK,CAAC,GAAG,aAAaP,KAAK,CAACmB,aAAa,CAAC,QAAQ,EAAE;IAC5DJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,WAAW,CAAC;IAC5CiB,OAAO,EAAEX;EACX,CAAC,EAAE,MAAM,CAAC,GAAG,IAAI,EAAEL,OAAO,KAAKC,KAAK,GAAG,CAAC,GAAG,aAAaR,KAAK,CAACmB,aAAa,CAAC,QAAQ,EAAE;IACpFJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,aAAa,CAAC;IAC9CiB,OAAO,EAAET;EACX,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAad,KAAK,CAACmB,aAAa,CAAC,QAAQ,EAAE;IACxDJ,SAAS,EAAE,EAAE,CAACK,MAAM,CAACd,SAAS,EAAE,WAAW,CAAC;IAC5CiB,OAAO,EAAEV;EACX,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}