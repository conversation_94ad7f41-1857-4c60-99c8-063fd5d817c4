{"ast": null, "code": "export var forceReflow = function forceReflow(node) {\n  return node.scrollTop;\n};", "map": {"version": 3, "names": ["forceReflow", "node", "scrollTop"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/react-transition-group/esm/utils/reflow.js"], "sourcesContent": ["export var forceReflow = function forceReflow(node) {\n  return node.scrollTop;\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAClD,OAAOA,IAAI,CAACC,SAAS;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}