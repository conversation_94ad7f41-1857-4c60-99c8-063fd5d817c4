{"ast": null, "code": "export default function (a, b) {\n  return a = +a, b = +b, function (t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}", "map": {"version": 3, "names": ["a", "b", "t", "Math", "round"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-interpolate/src/round.js"], "sourcesContent": ["export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC,EAAE,UAASC,CAAC,EAAE;IACjC,OAAOC,IAAI,CAACC,KAAK,CAACJ,CAAC,IAAI,CAAC,GAAGE,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,CAAC;EACxC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}