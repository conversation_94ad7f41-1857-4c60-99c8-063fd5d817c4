{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdPlacement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\n\n// Extend the Window interface to include adsbygoogle\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getAdSizeConfig = size => {\n  switch (size) {\n    case 'responsive':\n      return 'data-ad-format=\"auto\" data-full-width-responsive=\"true\"';\n    case 'auto':\n      return 'data-ad-format=\"auto\"';\n    case 'horizontal':\n      return 'data-ad-format=\"horizontal\"';\n    case 'rectangle':\n      return 'data-ad-format=\"rectangle\"';\n    case 'vertical':\n      return 'data-ad-format=\"vertical\"';\n    case 'leaderboard':\n      return '';\n    default:\n      return 'data-ad-format=\"auto\"';\n  }\n};\nconst AdPlacement = ({\n  adClient = 'ca-pub-xxxxxxxxxxxxxxxx',\n  // Replace with your actual AdSense client ID\n  adSlot,\n  adSize,\n  adFormat,\n  className = '',\n  style = {},\n  responsive = true,\n  fullWidth = false\n}) => {\n  _s();\n  const adRef = useRef(null);\n  const adSizeConfig = getAdSizeConfig(adSize);\n  useEffect(() => {\n    // Only load ads in production\n    if (process.env.NODE_ENV === 'production') {\n      try {\n        // Check if Google AdSense script is already loaded\n        if (typeof window !== 'undefined' && window.adsbygoogle) {\n          // Push the ad to Google AdSense\n          (window.adsbygoogle = window.adsbygoogle || []).push({});\n        } else {\n          console.warn('AdSense not available');\n        }\n      } catch (error) {\n        console.error('Error loading AdSense ad:', error);\n      }\n    }\n  }, []);\n\n  // In development, show a placeholder\n  if (process.env.NODE_ENV !== 'production') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `ad-placeholder ${className}`,\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f0f0f0',\n        border: '1px dashed #ccc',\n        borderRadius: '8px',\n        padding: '20px',\n        textAlign: 'center',\n        color: '#666',\n        fontSize: '14px',\n        minHeight: adSize === 'leaderboard' ? '90px' : '250px',\n        width: adSize === 'leaderboard' ? '728px' : '100%',\n        maxWidth: '100%',\n        margin: '0 auto',\n        ...style\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-medium\",\n          children: \"Ad Placement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs mt-1\",\n          children: [\"Size: \", adSize]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs mt-1\",\n          children: [\"Slot: \", adSlot]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n\n  // In production, render the actual ad\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: adRef,\n    className: `ad-container ${className}`,\n    style: {\n      display: 'block',\n      textAlign: 'center',\n      margin: '20px auto',\n      overflow: 'hidden',\n      ...style\n    },\n    children: /*#__PURE__*/_jsxDEV(\"ins\", {\n      className: \"adsbygoogle\",\n      style: {\n        display: 'block',\n        width: fullWidth ? '100%' : undefined,\n        height: adSize === 'leaderboard' ? '90px' : '100%',\n        minHeight: adSize === 'vertical' ? '600px' : undefined\n      },\n      \"data-ad-client\": adClient,\n      \"data-ad-slot\": adSlot,\n      \"data-full-width-responsive\": responsive ? 'true' : 'false',\n      ...(adFormat && {\n        'data-ad-format': adFormat\n      }),\n      dangerouslySetInnerHTML: {\n        __html: ' '\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n_s(AdPlacement, \"UdpN7xBEWYn9NQkm3vObuBIb4OU=\");\n_c = AdPlacement;\nexport default AdPlacement;\nvar _c;\n$RefreshReg$(_c, \"AdPlacement\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "getAdSizeConfig", "size", "AdPlacement", "adClient", "adSlot", "adSize", "adFormat", "className", "style", "responsive", "fullWidth", "_s", "adRef", "adSizeConfig", "process", "env", "NODE_ENV", "window", "adsbygoogle", "push", "console", "warn", "error", "display", "alignItems", "justifyContent", "backgroundColor", "border", "borderRadius", "padding", "textAlign", "color", "fontSize", "minHeight", "width", "max<PERSON><PERSON><PERSON>", "margin", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "overflow", "undefined", "height", "dangerouslySetInnerHTML", "__html", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdPlacement.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\n// Extend the Window interface to include adsbygoogle\ndeclare global {\n  interface Window {\n    adsbygoogle: any[];\n  }\n}\n\nexport type AdSize = \n  | 'responsive'\n  | 'auto'\n  | 'horizontal'\n  | 'rectangle'\n  | 'vertical'\n  | 'leaderboard';\n\ninterface AdPlacementProps {\n  adClient?: string;\n  adSlot: string;\n  adSize: AdSize;\n  adFormat?: 'auto' | 'fluid' | 'rectangle';\n  className?: string;\n  style?: React.CSSProperties;\n  responsive?: boolean;\n  fullWidth?: boolean;\n}\n\nconst getAdSizeConfig = (size: AdSize): string => {\n  switch (size) {\n    case 'responsive':\n      return 'data-ad-format=\"auto\" data-full-width-responsive=\"true\"';\n    case 'auto':\n      return 'data-ad-format=\"auto\"';\n    case 'horizontal':\n      return 'data-ad-format=\"horizontal\"';\n    case 'rectangle':\n      return 'data-ad-format=\"rectangle\"';\n    case 'vertical':\n      return 'data-ad-format=\"vertical\"';\n    case 'leaderboard':\n      return '';\n    default:\n      return 'data-ad-format=\"auto\"';\n  }\n};\n\nconst AdPlacement: React.FC<AdPlacementProps> = ({\n  adClient = 'ca-pub-xxxxxxxxxxxxxxxx', // Replace with your actual AdSense client ID\n  adSlot,\n  adSize,\n  adFormat,\n  className = '',\n  style = {},\n  responsive = true,\n  fullWidth = false,\n}) => {\n  const adRef = useRef<HTMLDivElement>(null);\n  const adSizeConfig = getAdSizeConfig(adSize);\n\n  useEffect(() => {\n    // Only load ads in production\n    if (process.env.NODE_ENV === 'production') {\n      try {\n        // Check if Google AdSense script is already loaded\n        if (typeof window !== 'undefined' && window.adsbygoogle) {\n          // Push the ad to Google AdSense\n          (window.adsbygoogle = window.adsbygoogle || []).push({});\n        } else {\n          console.warn('AdSense not available');\n        }\n      } catch (error) {\n        console.error('Error loading AdSense ad:', error);\n      }\n    }\n  }, []);\n\n  // In development, show a placeholder\n  if (process.env.NODE_ENV !== 'production') {\n    return (\n      <div\n        className={`ad-placeholder ${className}`}\n        style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#f0f0f0',\n          border: '1px dashed #ccc',\n          borderRadius: '8px',\n          padding: '20px',\n          textAlign: 'center',\n          color: '#666',\n          fontSize: '14px',\n          minHeight: adSize === 'leaderboard' ? '90px' : '250px',\n          width: adSize === 'leaderboard' ? '728px' : '100%',\n          maxWidth: '100%',\n          margin: '0 auto',\n          ...style,\n        }}\n      >\n        <div>\n          <div className=\"text-sm font-medium\">Ad Placement</div>\n          <div className=\"text-xs mt-1\">Size: {adSize}</div>\n          <div className=\"text-xs mt-1\">Slot: {adSlot}</div>\n        </div>\n      </div>\n    );\n  }\n\n  // In production, render the actual ad\n  return (\n    <div\n      ref={adRef}\n      className={`ad-container ${className}`}\n      style={{\n        display: 'block',\n        textAlign: 'center',\n        margin: '20px auto',\n        overflow: 'hidden',\n        ...style,\n      }}\n    >\n      <ins\n        className=\"adsbygoogle\"\n        style={{\n          display: 'block',\n          width: fullWidth ? '100%' : undefined,\n          height: adSize === 'leaderboard' ? '90px' : '100%',\n          minHeight: adSize === 'vertical' ? '600px' : undefined,\n        }}\n        data-ad-client={adClient}\n        data-ad-slot={adSlot}\n        data-full-width-responsive={responsive ? 'true' : 'false'}\n        {...(adFormat && { 'data-ad-format': adFormat })}\n        dangerouslySetInnerHTML={{\n          __html: ' ',\n        }}\n      />\n    </div>\n  );\n};\n\nexport default AdPlacement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AA0BA,MAAMC,eAAe,GAAIC,IAAY,IAAa;EAChD,QAAQA,IAAI;IACV,KAAK,YAAY;MACf,OAAO,yDAAyD;IAClE,KAAK,MAAM;MACT,OAAO,uBAAuB;IAChC,KAAK,YAAY;MACf,OAAO,6BAA6B;IACtC,KAAK,WAAW;MACd,OAAO,4BAA4B;IACrC,KAAK,UAAU;MACb,OAAO,2BAA2B;IACpC,KAAK,aAAa;MAChB,OAAO,EAAE;IACX;MACE,OAAO,uBAAuB;EAClC;AACF,CAAC;AAED,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ,GAAG,yBAAyB;EAAE;EACtCC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAGf,MAAM,CAAiB,IAAI,CAAC;EAC1C,MAAMgB,YAAY,GAAGb,eAAe,CAACK,MAAM,CAAC;EAE5CT,SAAS,CAAC,MAAM;IACd;IACA,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI;QACF;QACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,WAAW,EAAE;UACvD;UACA,CAACD,MAAM,CAACC,WAAW,GAAGD,MAAM,CAACC,WAAW,IAAI,EAAE,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,MAAM;UACLC,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAAC;QACvC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,oBACEjB,OAAA;MACEQ,SAAS,EAAE,kBAAkBA,SAAS,EAAG;MACzCC,KAAK,EAAE;QACLe,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE5B,MAAM,KAAK,aAAa,GAAG,MAAM,GAAG,OAAO;QACtD6B,KAAK,EAAE7B,MAAM,KAAK,aAAa,GAAG,OAAO,GAAG,MAAM;QAClD8B,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,QAAQ;QAChB,GAAG5B;MACL,CAAE;MAAA6B,QAAA,eAEFtC,OAAA;QAAAsC,QAAA,gBACEtC,OAAA;UAAKQ,SAAS,EAAC,qBAAqB;UAAA8B,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvD1C,OAAA;UAAKQ,SAAS,EAAC,cAAc;UAAA8B,QAAA,GAAC,QAAM,EAAChC,MAAM;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClD1C,OAAA;UAAKQ,SAAS,EAAC,cAAc;UAAA8B,QAAA,GAAC,QAAM,EAACjC,MAAM;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACE1C,OAAA;IACE2C,GAAG,EAAE9B,KAAM;IACXL,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAE;MACLe,OAAO,EAAE,OAAO;MAChBO,SAAS,EAAE,QAAQ;MACnBM,MAAM,EAAE,WAAW;MACnBO,QAAQ,EAAE,QAAQ;MAClB,GAAGnC;IACL,CAAE;IAAA6B,QAAA,eAEFtC,OAAA;MACEQ,SAAS,EAAC,aAAa;MACvBC,KAAK,EAAE;QACLe,OAAO,EAAE,OAAO;QAChBW,KAAK,EAAExB,SAAS,GAAG,MAAM,GAAGkC,SAAS;QACrCC,MAAM,EAAExC,MAAM,KAAK,aAAa,GAAG,MAAM,GAAG,MAAM;QAClD4B,SAAS,EAAE5B,MAAM,KAAK,UAAU,GAAG,OAAO,GAAGuC;MAC/C,CAAE;MACF,kBAAgBzC,QAAS;MACzB,gBAAcC,MAAO;MACrB,8BAA4BK,UAAU,GAAG,MAAM,GAAG,OAAQ;MAAA,IACrDH,QAAQ,IAAI;QAAE,gBAAgB,EAAEA;MAAS,CAAC;MAC/CwC,uBAAuB,EAAE;QACvBC,MAAM,EAAE;MACV;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA7FIT,WAAuC;AAAA8C,EAAA,GAAvC9C,WAAuC;AA+F7C,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}