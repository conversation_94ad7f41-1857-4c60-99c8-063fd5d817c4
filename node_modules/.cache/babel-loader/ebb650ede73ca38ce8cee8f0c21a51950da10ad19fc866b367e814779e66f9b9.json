{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SimplifiedSidebar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimplifiedSidebar = ({\n  config,\n  className = ''\n}) => {\n  _s();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        limit: config.limit.toString(),\n        exclude: config.currentItem\n      });\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await fetch(`${apiUrl}/api/scholarships/latest?${params}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      if (data.success) {\n        setScholarships(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching sidebar scholarships:', error);\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchScholarships();\n  }, [config.currentItem, config.limit]);\n  const getScholarshipUrl = scholarship => {\n    if (scholarship.slug) {\n      return `/bourse/${scholarship.slug}`;\n    }\n    return `/scholarships/${scholarship.id}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-xl shadow-lg p-6 ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-pulse space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-gray-200 rounded w-3/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-12 w-16 bg-gray-200 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white rounded-xl shadow-lg p-6 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: \"Derni\\xE8res Bourses\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-12 h-1 bg-blue-600 rounded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: scholarships.length > 0 ? scholarships.map(scholarship => /*#__PURE__*/_jsxDEV(Link, {\n        to: getScholarshipUrl(scholarship),\n        className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: scholarship.thumbnail || '/images/default-scholarship.jpg',\n            alt: scholarship.title,\n            className: \"h-12 w-16 object-cover rounded\",\n            onError: e => {\n              const target = e.target;\n              target.src = '/images/default-scholarship.jpg';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\",\n            children: scholarship.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 15\n        }, this)]\n      }, scholarship.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 mx-auto\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Aucune bourse disponible\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 p-4 bg-blue-50 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-sm font-semibold text-blue-900 mb-2\",\n        children: \"Newsletter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-blue-700 mb-3\",\n        children: \"Recevez les derni\\xE8res bourses directement dans votre bo\\xEEte mail.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          placeholder: \"Votre email\",\n          className: \"flex-1 px-3 py-2 text-xs border border-blue-200 rounded-l-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-3 py-2 bg-blue-600 text-white text-xs rounded-r-md hover:bg-blue-700 transition-colors duration-200\",\n          children: \"S'abonner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(SimplifiedSidebar, \"BPqLAIRjW7OFroFurMebosxJCqA=\");\n_c = SimplifiedSidebar;\nexport default SimplifiedSidebar;\nvar _c;\n$RefreshReg$(_c, \"SimplifiedSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "SimplifiedSidebar", "config", "className", "_s", "scholarships", "setScholarships", "loading", "setLoading", "fetchScholarships", "params", "URLSearchParams", "limit", "toString", "exclude", "currentItem", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "Error", "data", "json", "success", "error", "console", "getScholarshipUrl", "scholarship", "slug", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "length", "to", "src", "thumbnail", "alt", "title", "onError", "e", "target", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SimplifiedSidebar.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  thumbnail: string;\n  slug?: string;\n}\n\ninterface SimplifiedSidebarProps {\n  config: {\n    type: 'levels' | 'countries' | 'opportunities';\n    currentItem: string;\n    limit: number;\n  };\n  className?: string;\n}\n\nconst SimplifiedSidebar: React.FC<SimplifiedSidebarProps> = ({ config, className = '' }) => {\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      \n      const params = new URLSearchParams({\n        limit: config.limit.toString(),\n        exclude: config.currentItem\n      });\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await fetch(`${apiUrl}/api/scholarships/latest?${params}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setScholarships(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching sidebar scholarships:', error);\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchScholarships();\n  }, [config.currentItem, config.limit]);\n\n  const getScholarshipUrl = (scholarship: Scholarship) => {\n    if (scholarship.slug) {\n      return `/bourse/${scholarship.slug}`;\n    }\n    return `/scholarships/${scholarship.id}`;\n  };\n\n  if (loading) {\n    return (\n      <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>\n        <div className=\"animate-pulse space-y-4\">\n          <div className=\"h-6 bg-gray-200 rounded w-3/4\"></div>\n          <div className=\"space-y-3\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3\">\n                <div className=\"h-12 w-16 bg-gray-200 rounded\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>\n      {/* Header */}\n      <div className=\"mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n          Dernières Bourses\n        </h3>\n        <div className=\"w-12 h-1 bg-blue-600 rounded\"></div>\n      </div>\n\n      {/* Scholarships List */}\n      <div className=\"space-y-4\">\n        {scholarships.length > 0 ? (\n          scholarships.map((scholarship) => (\n            <Link\n              key={scholarship.id}\n              to={getScholarshipUrl(scholarship)}\n              className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group\"\n            >\n              {/* Thumbnail */}\n              <div className=\"flex-shrink-0\">\n                <img\n                  src={scholarship.thumbnail || '/images/default-scholarship.jpg'}\n                  alt={scholarship.title}\n                  className=\"h-12 w-16 object-cover rounded\"\n                  onError={(e) => {\n                    const target = e.target as HTMLImageElement;\n                    target.src = '/images/default-scholarship.jpg';\n                  }}\n                />\n              </div>\n              \n              {/* Title */}\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                  {scholarship.title}\n                </h4>\n              </div>\n            </Link>\n          ))\n        ) : (\n          <div className=\"text-center py-8\">\n            <div className=\"text-gray-400 mb-2\">\n              <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <p className=\"text-sm text-gray-500\">Aucune bourse disponible</p>\n          </div>\n        )}\n      </div>\n\n      {/* Newsletter Subscription */}\n      <div className=\"mt-8 p-4 bg-blue-50 rounded-lg\">\n        <h4 className=\"text-sm font-semibold text-blue-900 mb-2\">\n          Newsletter\n        </h4>\n        <p className=\"text-xs text-blue-700 mb-3\">\n          Recevez les dernières bourses directement dans votre boîte mail.\n        </p>\n        <div className=\"flex\">\n          <input\n            type=\"email\"\n            placeholder=\"Votre email\"\n            className=\"flex-1 px-3 py-2 text-xs border border-blue-200 rounded-l-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n          />\n          <button className=\"px-3 py-2 bg-blue-600 text-white text-xs rounded-r-md hover:bg-blue-700 transition-colors duration-200\">\n            S'abonner\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimplifiedSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBxC,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC1F,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMa,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAME,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,KAAK,EAAEV,MAAM,CAACU,KAAK,CAACC,QAAQ,CAAC,CAAC;QAC9BC,OAAO,EAAEZ,MAAM,CAACa;MAClB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,4BAA4BN,MAAM,EAAE,CAAC;MAE3E,IAAI,CAACU,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBpB,eAAe,CAACkB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MAClC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DrB,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDX,SAAS,CAAC,MAAM;IACdY,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACP,MAAM,CAACa,WAAW,EAAEb,MAAM,CAACU,KAAK,CAAC,CAAC;EAEtC,MAAMiB,iBAAiB,GAAIC,WAAwB,IAAK;IACtD,IAAIA,WAAW,CAACC,IAAI,EAAE;MACpB,OAAO,WAAWD,WAAW,CAACC,IAAI,EAAE;IACtC;IACA,OAAO,iBAAiBD,WAAW,CAACE,EAAE,EAAE;EAC1C,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKG,SAAS,EAAE,qCAAqCA,SAAS,EAAG;MAAA8B,QAAA,eAC/DjC,OAAA;QAAKG,SAAS,EAAC,yBAAyB;QAAA8B,QAAA,gBACtCjC,OAAA;UAAKG,SAAS,EAAC;QAA+B;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDrC,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAA8B,QAAA,EACvB,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBzC,OAAA;YAAaG,SAAS,EAAC,6BAA6B;YAAA8B,QAAA,gBAClDjC,OAAA;cAAKG,SAAS,EAAC;YAA+B;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDrC,OAAA;cAAKG,SAAS,EAAC,QAAQ;cAAA8B,QAAA,eACrBjC,OAAA;gBAAKG,SAAS,EAAC;cAAgC;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA,GAJEI,CAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKN,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAKG,SAAS,EAAE,qCAAqCA,SAAS,EAAG;IAAA8B,QAAA,gBAE/DjC,OAAA;MAAKG,SAAS,EAAC,MAAM;MAAA8B,QAAA,gBACnBjC,OAAA;QAAIG,SAAS,EAAC,0CAA0C;QAAA8B,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrC,OAAA;QAAKG,SAAS,EAAC;MAA8B;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNrC,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAA8B,QAAA,EACvB5B,YAAY,CAACqC,MAAM,GAAG,CAAC,GACtBrC,YAAY,CAACkC,GAAG,CAAET,WAAW,iBAC3B9B,OAAA,CAACF,IAAI;QAEH6C,EAAE,EAAEd,iBAAiB,CAACC,WAAW,CAAE;QACnC3B,SAAS,EAAC,kGAAkG;QAAA8B,QAAA,gBAG5GjC,OAAA;UAAKG,SAAS,EAAC,eAAe;UAAA8B,QAAA,eAC5BjC,OAAA;YACE4C,GAAG,EAAEd,WAAW,CAACe,SAAS,IAAI,iCAAkC;YAChEC,GAAG,EAAEhB,WAAW,CAACiB,KAAM;YACvB5C,SAAS,EAAC,gCAAgC;YAC1C6C,OAAO,EAAGC,CAAC,IAAK;cACd,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;cAC3CA,MAAM,CAACN,GAAG,GAAG,iCAAiC;YAChD;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrC,OAAA;UAAKG,SAAS,EAAC,gBAAgB;UAAA8B,QAAA,eAC7BjC,OAAA;YAAIG,SAAS,EAAC,yGAAyG;YAAA8B,QAAA,EACpHH,WAAW,CAACiB;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GAtBDP,WAAW,CAACE,EAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBf,CACP,CAAC,gBAEFrC,OAAA;QAAKG,SAAS,EAAC,kBAAkB;QAAA8B,QAAA,gBAC/BjC,OAAA;UAAKG,SAAS,EAAC,oBAAoB;UAAA8B,QAAA,eACjCjC,OAAA;YAAKG,SAAS,EAAC,mBAAmB;YAACgD,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAApB,QAAA,eACtFjC,OAAA;cAAMsD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsH;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3L;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrC,OAAA;UAAGG,SAAS,EAAC,uBAAuB;UAAA8B,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNrC,OAAA;MAAKG,SAAS,EAAC,gCAAgC;MAAA8B,QAAA,gBAC7CjC,OAAA;QAAIG,SAAS,EAAC,0CAA0C;QAAA8B,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrC,OAAA;QAAGG,SAAS,EAAC,4BAA4B;QAAA8B,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrC,OAAA;QAAKG,SAAS,EAAC,MAAM;QAAA8B,QAAA,gBACnBjC,OAAA;UACE0D,IAAI,EAAC,OAAO;UACZC,WAAW,EAAC,aAAa;UACzBxD,SAAS,EAAC;QAAkH;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7H,CAAC,eACFrC,OAAA;UAAQG,SAAS,EAAC,wGAAwG;UAAA8B,QAAA,EAAC;QAE3H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAzIIH,iBAAmD;AAAA2D,EAAA,GAAnD3D,iBAAmD;AA2IzD,eAAeA,iBAAiB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}