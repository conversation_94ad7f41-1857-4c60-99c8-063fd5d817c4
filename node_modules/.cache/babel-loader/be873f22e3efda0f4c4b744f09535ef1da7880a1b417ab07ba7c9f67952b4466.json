{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Helper function to get country flag\nconst getCountryFlag = countryName => {\n  const flagMap = {\n    'France': '🇫🇷',\n    'Canada': '🇨🇦',\n    'États-Unis': '🇺🇸',\n    'Allemagne': '🇩🇪',\n    'Royaume-Uni': '🇬🇧',\n    'Australie': '🇦🇺',\n    'Suisse': '🇨🇭',\n    'Belgique': '🇧🇪',\n    'Pays-Bas': '🇳🇱',\n    'Suède': '🇸🇪',\n    'Norvège': '🇳🇴',\n    'Danemark': '🇩🇰',\n    'Finlande': '🇫🇮',\n    'Japon': '🇯🇵',\n    'Corée du Sud': '🇰🇷',\n    'Singapour': '🇸🇬',\n    'Nouvelle-Zélande': '🇳🇿',\n    'Italie': '🇮🇹',\n    'Espagne': '🇪🇸',\n    'Portugal': '🇵🇹'\n  };\n  return flagMap[countryName] || '🌍';\n};\nconst CountryDetail = () => {\n  _s();\n  const {\n    country\n  } = useParams();\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n  const pageConfig = {\n    country: decodedCountry,\n    title: `Bourses d'Études en ${decodedCountry} | Opportunités de Financement`,\n    description: `Découvrez toutes les bourses d'études disponibles en ${decodedCountry}. Financez vos études supérieures avec des opportunités de bourses prestigieuses.`,\n    keywords: `bourses ${decodedCountry}, financement études ${decodedCountry}, études supérieures ${decodedCountry}`,\n    heroTitle: 'Bourses d\\'Études en',\n    heroSubtitle: `Découvrez toutes les opportunités de bourses d'études disponibles en ${decodedCountry} et financez votre avenir académique.`,\n    infoTitle: `Pourquoi Étudier en ${decodedCountry} ?`,\n    infoContent: `${decodedCountry} offre un système éducatif de qualité mondiale avec des universités prestigieuses et des opportunités de recherche exceptionnelles. Avec une bourse d'études, vous pouvez accéder à cette excellence académique sans contraintes financières.`,\n    benefits: ['Universités de renommée mondiale', 'Programmes académiques d\\'excellence', 'Environnement multiculturel enrichissant', 'Opportunités de recherche avancée', 'Réseau professionnel international', 'Expérience culturelle unique'],\n    apiEndpoint: '/api/scholarships/search',\n    flag: getCountryFlag(decodedCountry)\n  };\n  return /*#__PURE__*/_jsxDEV(ProfessionalPageLayout, {\n    hero: {\n      title: decodedCountry,\n      subtitle: \"Découvrez toutes les bourses d'études disponibles\",\n      icon: sidebarService.getCountryFlag(decodedCountry),\n      backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'\n    },\n    statistics: statistics ? {\n      total: statistics.totalScholarships,\n      active: statistics.openScholarships,\n      inactive: statistics.closedScholarships,\n      label: 'Total des bourses',\n      activeLabel: 'Bourses ouvertes',\n      inactiveLabel: 'Bourses fermées'\n    } : undefined,\n    sidebarConfig: {\n      type: 'countries',\n      currentItem: decodedCountry,\n      limit: 15\n    },\n    children: /*#__PURE__*/_jsxDEV(ProfessionalContentGrid, {\n      items: scholarships,\n      loading: loading,\n      emptyMessage: \"Aucune bourse n'est actuellement disponible pour ce pays.\",\n      emptyIcon: \"\\uD83D\\uDCDA\",\n      renderItem: renderScholarshipItem,\n      pagination: {\n        currentPage,\n        totalPages,\n        onPageChange: handlePageChange\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(CountryDetail, \"llnm9w59V8WVzyDr3L/T8v5CIos=\", false, function () {\n  return [useParams];\n});\n_c = CountryDetail;\nexport default CountryDetail;\nvar _c;\n$RefreshReg$(_c, \"CountryDetail\");", "map": {"version": 3, "names": ["React", "useParams", "jsxDEV", "_jsxDEV", "getCountryFlag", "countryName", "flagMap", "CountryDetail", "_s", "country", "decodedCountry", "decodeURIComponent", "pageConfig", "title", "description", "keywords", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "infoTitle", "infoContent", "benefits", "apiEndpoint", "flag", "ProfessionalPageLayout", "hero", "subtitle", "icon", "sidebarService", "backgroundColor", "statistics", "total", "totalScholarships", "active", "openScholarships", "inactive", "closedScholarships", "label", "activeLabel", "inactiveLabel", "undefined", "sidebarConfig", "type", "currentItem", "limit", "children", "ProfessionalContentGrid", "items", "scholarships", "loading", "emptyMessage", "emptyIcon", "renderItem", "renderScholarshipItem", "pagination", "currentPage", "totalPages", "onPageChange", "handlePageChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\nimport StandardCountryPage from '../components/StandardCountryPage';\n\n// Helper function to get country flag\nconst getCountryFlag = (countryName: string): string => {\n  const flagMap: { [key: string]: string } = {\n    'France': '🇫🇷',\n    'Canada': '🇨🇦',\n    'États-Unis': '🇺🇸',\n    'Allemagne': '🇩🇪',\n    'Royaume-Uni': '🇬🇧',\n    'Australie': '🇦🇺',\n    'Suisse': '🇨🇭',\n    'Belgique': '🇧🇪',\n    'Pays-Bas': '🇳🇱',\n    'Suède': '🇸🇪',\n    'Norvège': '🇳🇴',\n    'Danemark': '🇩🇰',\n    'Finlande': '🇫🇮',\n    'Japon': '🇯🇵',\n    'Corée du Sud': '🇰🇷',\n    'Singapour': '🇸🇬',\n    'Nouvelle-Zélande': '🇳🇿',\n    'Italie': '🇮🇹',\n    'Espagne': '🇪🇸',\n    'Portugal': '🇵🇹'\n  };\n  return flagMap[countryName] || '🌍';\n};\n\nconst CountryDetail: React.FC = () => {\n  const { country } = useParams<{ country: string }>();\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n\n  const pageConfig = {\n    country: decodedCountry,\n    title: `Bourses d'Études en ${decodedCountry} | Opportunités de Financement`,\n    description: `Découvrez toutes les bourses d'études disponibles en ${decodedCountry}. Financez vos études supérieures avec des opportunités de bourses prestigieuses.`,\n    keywords: `bourses ${decodedCountry}, financement études ${decodedCountry}, études supérieures ${decodedCountry}`,\n    heroTitle: 'Bourses d\\'Études en',\n    heroSubtitle: `Découvrez toutes les opportunités de bourses d'études disponibles en ${decodedCountry} et financez votre avenir académique.`,\n    infoTitle: `Pourquoi Étudier en ${decodedCountry} ?`,\n    infoContent: `${decodedCountry} offre un système éducatif de qualité mondiale avec des universités prestigieuses et des opportunités de recherche exceptionnelles. Avec une bourse d'études, vous pouvez accéder à cette excellence académique sans contraintes financières.`,\n    benefits: [\n      'Universités de renommée mondiale',\n      'Programmes académiques d\\'excellence',\n      'Environnement multiculturel enrichissant',\n      'Opportunités de recherche avancée',\n      'Réseau professionnel international',\n      'Expérience culturelle unique'\n    ],\n    apiEndpoint: '/api/scholarships/search',\n    flag: getCountryFlag(decodedCountry)\n  };\n\n  return (\n    <ProfessionalPageLayout\n      hero={{\n        title: decodedCountry,\n        subtitle: \"Découvrez toutes les bourses d'études disponibles\",\n        icon: sidebarService.getCountryFlag(decodedCountry),\n        backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'\n      }}\n      statistics={statistics ? {\n        total: statistics.totalScholarships,\n        active: statistics.openScholarships,\n        inactive: statistics.closedScholarships,\n        label: 'Total des bourses',\n        activeLabel: 'Bourses ouvertes',\n        inactiveLabel: 'Bourses fermées'\n      } : undefined}\n      sidebarConfig={{\n        type: 'countries',\n        currentItem: decodedCountry,\n        limit: 15\n      }}\n    >\n      <ProfessionalContentGrid\n        items={scholarships}\n        loading={loading}\n        emptyMessage=\"Aucune bourse n'est actuellement disponible pour ce pays.\"\n        emptyIcon=\"📚\"\n        renderItem={renderScholarshipItem}\n        pagination={{\n          currentPage,\n          totalPages,\n          onPageChange: handlePageChange\n        }}\n      />\n    </ProfessionalPageLayout>\n\n  );\n};\n\nexport default CountryDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG7C;AACA,MAAMC,cAAc,GAAIC,WAAmB,IAAa;EACtD,MAAMC,OAAkC,GAAG;IACzC,QAAQ,EAAE,MAAM;IAChB,QAAQ,EAAE,MAAM;IAChB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAM;IACf,SAAS,EAAE,MAAM;IACjB,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAM;IACf,cAAc,EAAE,MAAM;IACtB,WAAW,EAAE,MAAM;IACnB,kBAAkB,EAAE,MAAM;IAC1B,QAAQ,EAAE,MAAM;IAChB,SAAS,EAAE,MAAM;IACjB,UAAU,EAAE;EACd,CAAC;EACD,OAAOA,OAAO,CAACD,WAAW,CAAC,IAAI,IAAI;AACrC,CAAC;AAED,MAAME,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAQ,CAAC,GAAGR,SAAS,CAAsB,CAAC;EACpD,MAAMS,cAAc,GAAGD,OAAO,GAAGE,kBAAkB,CAACF,OAAO,CAAC,GAAG,EAAE;EAEjE,MAAMG,UAAU,GAAG;IACjBH,OAAO,EAAEC,cAAc;IACvBG,KAAK,EAAE,uBAAuBH,cAAc,gCAAgC;IAC5EI,WAAW,EAAE,wDAAwDJ,cAAc,mFAAmF;IACtKK,QAAQ,EAAE,WAAWL,cAAc,wBAAwBA,cAAc,wBAAwBA,cAAc,EAAE;IACjHM,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,wEAAwEP,cAAc,uCAAuC;IAC3IQ,SAAS,EAAE,uBAAuBR,cAAc,IAAI;IACpDS,WAAW,EAAE,GAAGT,cAAc,+OAA+O;IAC7QU,QAAQ,EAAE,CACR,kCAAkC,EAClC,sCAAsC,EACtC,0CAA0C,EAC1C,mCAAmC,EACnC,oCAAoC,EACpC,8BAA8B,CAC/B;IACDC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAElB,cAAc,CAACM,cAAc;EACrC,CAAC;EAED,oBACEP,OAAA,CAACoB,sBAAsB;IACrBC,IAAI,EAAE;MACJX,KAAK,EAAEH,cAAc;MACrBe,QAAQ,EAAE,mDAAmD;MAC7DC,IAAI,EAAEC,cAAc,CAACvB,cAAc,CAACM,cAAc,CAAC;MACnDkB,eAAe,EAAE;IACnB,CAAE;IACFC,UAAU,EAAEA,UAAU,GAAG;MACvBC,KAAK,EAAED,UAAU,CAACE,iBAAiB;MACnCC,MAAM,EAAEH,UAAU,CAACI,gBAAgB;MACnCC,QAAQ,EAAEL,UAAU,CAACM,kBAAkB;MACvCC,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,kBAAkB;MAC/BC,aAAa,EAAE;IACjB,CAAC,GAAGC,SAAU;IACdC,aAAa,EAAE;MACbC,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAEhC,cAAc;MAC3BiC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,eAEFzC,OAAA,CAAC0C,uBAAuB;MACtBC,KAAK,EAAEC,YAAa;MACpBC,OAAO,EAAEA,OAAQ;MACjBC,YAAY,EAAC,2DAA2D;MACxEC,SAAS,EAAC,cAAI;MACdC,UAAU,EAAEC,qBAAsB;MAClCC,UAAU,EAAE;QACVC,WAAW;QACXC,UAAU;QACVC,YAAY,EAAEC;MAChB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACoB,CAAC;AAG7B,CAAC;AAACrD,EAAA,CA9DID,aAAuB;EAAA,QACPN,SAAS;AAAA;AAAA6D,EAAA,GADzBvD,aAAuB;AAgE7B,eAAeA,aAAa;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}