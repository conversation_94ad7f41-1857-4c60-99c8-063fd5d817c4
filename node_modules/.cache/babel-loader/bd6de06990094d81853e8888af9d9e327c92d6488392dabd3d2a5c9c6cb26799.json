{"ast": null, "code": "export default function (numerals) {\n  return function (value) {\n    return value.replace(/[0-9]/g, function (i) {\n      return numerals[+i];\n    });\n  };\n}", "map": {"version": 3, "names": ["numerals", "value", "replace", "i"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-format/src/formatNumerals.js"], "sourcesContent": ["export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAE;EAChC,OAAO,UAASC,KAAK,EAAE;IACrB,OAAOA,KAAK,CAACC,OAAO,CAAC,QAAQ,EAAE,UAASC,CAAC,EAAE;MACzC,OAAOH,QAAQ,CAAC,CAACG,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}