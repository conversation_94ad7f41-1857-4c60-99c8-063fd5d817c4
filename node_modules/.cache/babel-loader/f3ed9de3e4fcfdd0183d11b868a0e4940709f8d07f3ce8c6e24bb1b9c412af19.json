{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport HeaderRow from \"./HeaderRow\";\nfunction parseHeaderRows(rootColumns) {\n  var rows = [];\n  function fillRowCells(columns, colIndex) {\n    var rowIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    // Init rows\n    rows[rowIndex] = rows[rowIndex] || [];\n    var currentColIndex = colIndex;\n    var colSpans = columns.filter(Boolean).map(function (column) {\n      var cell = {\n        key: column.key,\n        className: column.className || '',\n        children: column.title,\n        column: column,\n        colStart: currentColIndex\n      };\n      var colSpan = 1;\n      var subColumns = column.children;\n      if (subColumns && subColumns.length > 0) {\n        colSpan = fillRowCells(subColumns, currentColIndex, rowIndex + 1).reduce(function (total, count) {\n          return total + count;\n        }, 0);\n        cell.hasSubColumns = true;\n      }\n      if ('colSpan' in column) {\n        colSpan = column.colSpan;\n      }\n      if ('rowSpan' in column) {\n        cell.rowSpan = column.rowSpan;\n      }\n      cell.colSpan = colSpan;\n      cell.colEnd = cell.colStart + colSpan - 1;\n      rows[rowIndex].push(cell);\n      currentColIndex += colSpan;\n      return colSpan;\n    });\n    return colSpans;\n  }\n\n  // Generate `rows` cell data\n  fillRowCells(rootColumns, 0);\n\n  // Handle `rowSpan`\n  var rowCount = rows.length;\n  var _loop = function _loop(rowIndex) {\n    rows[rowIndex].forEach(function (cell) {\n      if (!('rowSpan' in cell) && !cell.hasSubColumns) {\n        // eslint-disable-next-line no-param-reassign\n        cell.rowSpan = rowCount - rowIndex;\n      }\n    });\n  };\n  for (var rowIndex = 0; rowIndex < rowCount; rowIndex += 1) {\n    _loop(rowIndex);\n  }\n  return rows;\n}\nvar Header = function Header(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var stickyOffsets = props.stickyOffsets,\n    columns = props.columns,\n    flattenColumns = props.flattenColumns,\n    onHeaderRow = props.onHeaderRow;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent;\n  var rows = React.useMemo(function () {\n    return parseHeaderRows(columns);\n  }, [columns]);\n  var WrapperComponent = getComponent(['header', 'wrapper'], 'thead');\n  var trComponent = getComponent(['header', 'row'], 'tr');\n  var thComponent = getComponent(['header', 'cell'], 'th');\n  return /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-thead\")\n  }, rows.map(function (row, rowIndex) {\n    var rowNode = /*#__PURE__*/React.createElement(HeaderRow, {\n      key: rowIndex,\n      flattenColumns: flattenColumns,\n      cells: row,\n      stickyOffsets: stickyOffsets,\n      rowComponent: trComponent,\n      cellComponent: thComponent,\n      onHeaderRow: onHeaderRow,\n      index: rowIndex\n    });\n    return rowNode;\n  }));\n};\nexport default responseImmutable(Header);", "map": {"version": 3, "names": ["useContext", "React", "TableContext", "responseImmutable", "devRenderTimes", "HeaderRow", "parseHeaderRows", "rootColumns", "rows", "fill<PERSON><PERSON><PERSON><PERSON>s", "columns", "colIndex", "rowIndex", "arguments", "length", "undefined", "currentColIndex", "colSpans", "filter", "Boolean", "map", "column", "cell", "key", "className", "children", "title", "colStart", "colSpan", "subColumns", "reduce", "total", "count", "hasSubColumns", "rowSpan", "colEnd", "push", "rowCount", "_loop", "for<PERSON>ach", "Header", "props", "process", "env", "NODE_ENV", "stickyOffsets", "flattenColumns", "onHeaderRow", "_useContext", "prefixCls", "getComponent", "useMemo", "WrapperComponent", "trComponent", "thComponent", "createElement", "concat", "row", "rowNode", "cells", "rowComponent", "cellComponent", "index"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-table/es/Header/Header.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport HeaderRow from \"./HeaderRow\";\nfunction parseHeaderRows(rootColumns) {\n  var rows = [];\n  function fillRowCells(columns, colIndex) {\n    var rowIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    // Init rows\n    rows[rowIndex] = rows[rowIndex] || [];\n    var currentColIndex = colIndex;\n    var colSpans = columns.filter(Boolean).map(function (column) {\n      var cell = {\n        key: column.key,\n        className: column.className || '',\n        children: column.title,\n        column: column,\n        colStart: currentColIndex\n      };\n      var colSpan = 1;\n      var subColumns = column.children;\n      if (subColumns && subColumns.length > 0) {\n        colSpan = fillRowCells(subColumns, currentColIndex, rowIndex + 1).reduce(function (total, count) {\n          return total + count;\n        }, 0);\n        cell.hasSubColumns = true;\n      }\n      if ('colSpan' in column) {\n        colSpan = column.colSpan;\n      }\n      if ('rowSpan' in column) {\n        cell.rowSpan = column.rowSpan;\n      }\n      cell.colSpan = colSpan;\n      cell.colEnd = cell.colStart + colSpan - 1;\n      rows[rowIndex].push(cell);\n      currentColIndex += colSpan;\n      return colSpan;\n    });\n    return colSpans;\n  }\n\n  // Generate `rows` cell data\n  fillRowCells(rootColumns, 0);\n\n  // Handle `rowSpan`\n  var rowCount = rows.length;\n  var _loop = function _loop(rowIndex) {\n    rows[rowIndex].forEach(function (cell) {\n      if (!('rowSpan' in cell) && !cell.hasSubColumns) {\n        // eslint-disable-next-line no-param-reassign\n        cell.rowSpan = rowCount - rowIndex;\n      }\n    });\n  };\n  for (var rowIndex = 0; rowIndex < rowCount; rowIndex += 1) {\n    _loop(rowIndex);\n  }\n  return rows;\n}\nvar Header = function Header(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var stickyOffsets = props.stickyOffsets,\n    columns = props.columns,\n    flattenColumns = props.flattenColumns,\n    onHeaderRow = props.onHeaderRow;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent;\n  var rows = React.useMemo(function () {\n    return parseHeaderRows(columns);\n  }, [columns]);\n  var WrapperComponent = getComponent(['header', 'wrapper'], 'thead');\n  var trComponent = getComponent(['header', 'row'], 'tr');\n  var thComponent = getComponent(['header', 'cell'], 'th');\n  return /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-thead\")\n  }, rows.map(function (row, rowIndex) {\n    var rowNode = /*#__PURE__*/React.createElement(HeaderRow, {\n      key: rowIndex,\n      flattenColumns: flattenColumns,\n      cells: row,\n      stickyOffsets: stickyOffsets,\n      rowComponent: trComponent,\n      cellComponent: thComponent,\n      onHeaderRow: onHeaderRow,\n      index: rowIndex\n    });\n    return rowNode;\n  }));\n};\nexport default responseImmutable(Header);"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,eAAeA,CAACC,WAAW,EAAE;EACpC,IAAIC,IAAI,GAAG,EAAE;EACb,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACvC,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACpF;IACAL,IAAI,CAACI,QAAQ,CAAC,GAAGJ,IAAI,CAACI,QAAQ,CAAC,IAAI,EAAE;IACrC,IAAII,eAAe,GAAGL,QAAQ;IAC9B,IAAIM,QAAQ,GAAGP,OAAO,CAACQ,MAAM,CAACC,OAAO,CAAC,CAACC,GAAG,CAAC,UAAUC,MAAM,EAAE;MAC3D,IAAIC,IAAI,GAAG;QACTC,GAAG,EAAEF,MAAM,CAACE,GAAG;QACfC,SAAS,EAAEH,MAAM,CAACG,SAAS,IAAI,EAAE;QACjCC,QAAQ,EAAEJ,MAAM,CAACK,KAAK;QACtBL,MAAM,EAAEA,MAAM;QACdM,QAAQ,EAAEX;MACZ,CAAC;MACD,IAAIY,OAAO,GAAG,CAAC;MACf,IAAIC,UAAU,GAAGR,MAAM,CAACI,QAAQ;MAChC,IAAII,UAAU,IAAIA,UAAU,CAACf,MAAM,GAAG,CAAC,EAAE;QACvCc,OAAO,GAAGnB,YAAY,CAACoB,UAAU,EAAEb,eAAe,EAAEJ,QAAQ,GAAG,CAAC,CAAC,CAACkB,MAAM,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;UAC/F,OAAOD,KAAK,GAAGC,KAAK;QACtB,CAAC,EAAE,CAAC,CAAC;QACLV,IAAI,CAACW,aAAa,GAAG,IAAI;MAC3B;MACA,IAAI,SAAS,IAAIZ,MAAM,EAAE;QACvBO,OAAO,GAAGP,MAAM,CAACO,OAAO;MAC1B;MACA,IAAI,SAAS,IAAIP,MAAM,EAAE;QACvBC,IAAI,CAACY,OAAO,GAAGb,MAAM,CAACa,OAAO;MAC/B;MACAZ,IAAI,CAACM,OAAO,GAAGA,OAAO;MACtBN,IAAI,CAACa,MAAM,GAAGb,IAAI,CAACK,QAAQ,GAAGC,OAAO,GAAG,CAAC;MACzCpB,IAAI,CAACI,QAAQ,CAAC,CAACwB,IAAI,CAACd,IAAI,CAAC;MACzBN,eAAe,IAAIY,OAAO;MAC1B,OAAOA,OAAO;IAChB,CAAC,CAAC;IACF,OAAOX,QAAQ;EACjB;;EAEA;EACAR,YAAY,CAACF,WAAW,EAAE,CAAC,CAAC;;EAE5B;EACA,IAAI8B,QAAQ,GAAG7B,IAAI,CAACM,MAAM;EAC1B,IAAIwB,KAAK,GAAG,SAASA,KAAKA,CAAC1B,QAAQ,EAAE;IACnCJ,IAAI,CAACI,QAAQ,CAAC,CAAC2B,OAAO,CAAC,UAAUjB,IAAI,EAAE;MACrC,IAAI,EAAE,SAAS,IAAIA,IAAI,CAAC,IAAI,CAACA,IAAI,CAACW,aAAa,EAAE;QAC/C;QACAX,IAAI,CAACY,OAAO,GAAGG,QAAQ,GAAGzB,QAAQ;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EACD,KAAK,IAAIA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGyB,QAAQ,EAAEzB,QAAQ,IAAI,CAAC,EAAE;IACzD0B,KAAK,CAAC1B,QAAQ,CAAC;EACjB;EACA,OAAOJ,IAAI;AACb;AACA,IAAIgC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCxC,cAAc,CAACqC,KAAK,CAAC;EACvB;EACA,IAAII,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACrCnC,OAAO,GAAG+B,KAAK,CAAC/B,OAAO;IACvBoC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,WAAW,GAAGN,KAAK,CAACM,WAAW;EACjC,IAAIC,WAAW,GAAGhD,UAAU,CAACE,YAAY,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IACvE+C,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,YAAY,GAAGF,WAAW,CAACE,YAAY;EACzC,IAAI1C,IAAI,GAAGP,KAAK,CAACkD,OAAO,CAAC,YAAY;IACnC,OAAO7C,eAAe,CAACI,OAAO,CAAC;EACjC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,IAAI0C,gBAAgB,GAAGF,YAAY,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;EACnE,IAAIG,WAAW,GAAGH,YAAY,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;EACvD,IAAII,WAAW,GAAGJ,YAAY,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACxD,OAAO,aAAajD,KAAK,CAACsD,aAAa,CAACH,gBAAgB,EAAE;IACxD5B,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACP,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEzC,IAAI,CAACY,GAAG,CAAC,UAAUqC,GAAG,EAAE7C,QAAQ,EAAE;IACnC,IAAI8C,OAAO,GAAG,aAAazD,KAAK,CAACsD,aAAa,CAAClD,SAAS,EAAE;MACxDkB,GAAG,EAAEX,QAAQ;MACbkC,cAAc,EAAEA,cAAc;MAC9Ba,KAAK,EAAEF,GAAG;MACVZ,aAAa,EAAEA,aAAa;MAC5Be,YAAY,EAAEP,WAAW;MACzBQ,aAAa,EAAEP,WAAW;MAC1BP,WAAW,EAAEA,WAAW;MACxBe,KAAK,EAAElD;IACT,CAAC,CAAC;IACF,OAAO8C,OAAO;EAChB,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAevD,iBAAiB,CAACqC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}