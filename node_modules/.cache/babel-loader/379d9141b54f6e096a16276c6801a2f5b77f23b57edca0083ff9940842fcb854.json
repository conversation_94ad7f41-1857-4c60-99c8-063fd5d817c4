{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CountryDetail = () => {\n  _s();\n  const {\n    country\n  } = useParams();\n  const [scholarships, setScholarships] = useState([]);\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n  const fetchScholarships = React.useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '12'\n      });\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries/${encodeURIComponent(decodedCountry)}/scholarships?${params}`);\n      if (response.ok) {\n        var _data$pagination;\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.totalPages) || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [decodedCountry, currentPage]);\n  const fetchStatistics = React.useCallback(async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries/${encodeURIComponent(decodedCountry)}/statistics`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching statistics:', error);\n    }\n  }, [decodedCountry]);\n  useEffect(() => {\n    if (decodedCountry) {\n      fetchScholarships();\n      fetchStatistics();\n    }\n  }, [decodedCountry, fetchScholarships, fetchStatistics]);\n  const handleScholarshipClick = id => {\n    window.location.href = `/scholarships/${id}`;\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const renderScholarshipItem = scholarship => /*#__PURE__*/_jsxDEV(ScholarshipCard, {\n    id: scholarship.id,\n    title: scholarship.title,\n    thumbnail: scholarship.thumbnail || '',\n    deadline: scholarship.deadline,\n    isOpen: scholarship.isOpen,\n    country: scholarship.country,\n    onClick: handleScholarshipClick\n  }, scholarship.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(ProfessionalPageLayout, {\n    hero: {\n      title: decodedCountry,\n      subtitle: \"Découvrez toutes les bourses d'études disponibles\",\n      icon: sidebarService.getCountryFlag(decodedCountry),\n      backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'\n    },\n    statistics: statistics ? {\n      total: statistics.totalScholarships,\n      active: statistics.openScholarships,\n      inactive: statistics.closedScholarships,\n      label: 'Total des bourses',\n      activeLabel: 'Bourses ouvertes',\n      inactiveLabel: 'Bourses fermées'\n    } : undefined,\n    sidebarConfig: {\n      type: 'countries',\n      currentItem: decodedCountry,\n      limit: 15\n    },\n    children: /*#__PURE__*/_jsxDEV(ProfessionalContentGrid, {\n      items: scholarships,\n      loading: loading,\n      emptyMessage: \"Aucune bourse n'est actuellement disponible pour ce pays.\",\n      emptyIcon: \"\\uD83D\\uDCDA\",\n      renderItem: renderScholarshipItem,\n      pagination: {\n        currentPage,\n        totalPages,\n        onPageChange: handlePageChange\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(CountryDetail, \"4Qjxq6/kKQmYVZ08nJnuZ0ejJPo=\", false, function () {\n  return [useParams];\n});\n_c = CountryDetail;\nexport default CountryDetail;\nvar _c;\n$RefreshReg$(_c, \"CountryDetail\");", "map": {"version": 3, "names": ["React", "useParams", "jsxDEV", "_jsxDEV", "CountryDetail", "_s", "country", "scholarships", "setScholarships", "useState", "statistics", "setStatistics", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "decodedCountry", "decodeURIComponent", "fetchScholarships", "useCallback", "params", "URLSearchParams", "page", "toString", "limit", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "encodeURIComponent", "ok", "_data$pagination", "data", "json", "pagination", "error", "console", "fetchStatistics", "useEffect", "handleScholarshipClick", "id", "window", "location", "href", "handlePageChange", "renderScholarshipItem", "scholarship", "ScholarshipCard", "title", "thumbnail", "deadline", "isOpen", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ProfessionalPageLayout", "hero", "subtitle", "icon", "sidebarService", "getCountryFlag", "backgroundColor", "total", "totalScholarships", "active", "openScholarships", "inactive", "closedScholarships", "label", "activeLabel", "inactiveLabel", "undefined", "sidebarConfig", "type", "currentItem", "children", "ProfessionalContentGrid", "items", "emptyMessage", "emptyIcon", "renderItem", "onPageChange", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\nimport StandardCountryPage from '../components/StandardCountryPage';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  deadline: string;\n  isOpen: boolean;\n  country: string;\n  level?: string;\n}\n\ninterface CountryStatistics {\n  country: string;\n  totalScholarships: number;\n  openScholarships: number;\n  closedScholarships: number;\n  scholarshipsByLevel: Array<{\n    level: string;\n    count: number;\n  }>;\n}\n\nconst CountryDetail: React.FC = () => {\n  const { country } = useParams<{ country: string }>();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [statistics, setStatistics] = useState<CountryStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n\n  const fetchScholarships = React.useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '12'\n      });\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries/${encodeURIComponent(decodedCountry)}/scholarships?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [decodedCountry, currentPage]);\n\n  const fetchStatistics = React.useCallback(async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries/${encodeURIComponent(decodedCountry)}/statistics`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching statistics:', error);\n    }\n  }, [decodedCountry]);\n\n  useEffect(() => {\n    if (decodedCountry) {\n      fetchScholarships();\n      fetchStatistics();\n    }\n  }, [decodedCountry, fetchScholarships, fetchStatistics]);\n\n\n\n  const handleScholarshipClick = (id: number) => {\n    window.location.href = `/scholarships/${id}`;\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const renderScholarshipItem = (scholarship: Scholarship) => (\n    <ScholarshipCard\n      key={scholarship.id}\n      id={scholarship.id}\n      title={scholarship.title}\n      thumbnail={scholarship.thumbnail || ''}\n      deadline={scholarship.deadline}\n      isOpen={scholarship.isOpen}\n      country={scholarship.country}\n      onClick={handleScholarshipClick}\n    />\n  );\n\n  return (\n    <ProfessionalPageLayout\n      hero={{\n        title: decodedCountry,\n        subtitle: \"Découvrez toutes les bourses d'études disponibles\",\n        icon: sidebarService.getCountryFlag(decodedCountry),\n        backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'\n      }}\n      statistics={statistics ? {\n        total: statistics.totalScholarships,\n        active: statistics.openScholarships,\n        inactive: statistics.closedScholarships,\n        label: 'Total des bourses',\n        activeLabel: 'Bourses ouvertes',\n        inactiveLabel: 'Bourses fermées'\n      } : undefined}\n      sidebarConfig={{\n        type: 'countries',\n        currentItem: decodedCountry,\n        limit: 15\n      }}\n    >\n      <ProfessionalContentGrid\n        items={scholarships}\n        loading={loading}\n        emptyMessage=\"Aucune bourse n'est actuellement disponible pour ce pays.\"\n        emptyIcon=\"📚\"\n        renderItem={renderScholarshipItem}\n        pagination={{\n          currentPage,\n          totalPages,\n          onPageChange: handlePageChange\n        }}\n      />\n    </ProfessionalPageLayout>\n\n  );\n};\n\nexport default CountryDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAwB7C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAQ,CAAC,GAAGL,SAAS,CAAsB,CAAC;EACpD,MAAM,CAACM,YAAY,EAAEC,eAAe,CAAC,GAAGC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGF,QAAQ,CAA2B,IAAI,CAAC;EAC5E,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGJ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMS,cAAc,GAAGZ,OAAO,GAAGa,kBAAkB,CAACb,OAAO,CAAC,GAAG,EAAE;EAEjE,MAAMc,iBAAiB,GAAGpB,KAAK,CAACqB,WAAW,CAAC,YAAY;IACtD,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,IAAI,EAAEV,WAAW,CAACW,QAAQ,CAAC,CAAC;QAC5BC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,kBAAkBM,kBAAkB,CAACf,cAAc,CAAC,iBAAiBI,MAAM,EAAE,CAAC;MACpH,IAAIS,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAC,gBAAA;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClC7B,eAAe,CAAC4B,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChCnB,aAAa,CAAC,EAAAkB,gBAAA,GAAAC,IAAI,CAACE,UAAU,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBnB,UAAU,KAAI,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,cAAc,EAAEJ,WAAW,CAAC,CAAC;EAEjC,MAAM2B,eAAe,GAAGzC,KAAK,CAACqB,WAAW,CAAC,YAAY;IACpD,IAAI;MACF,MAAMM,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,kBAAkBM,kBAAkB,CAACf,cAAc,CAAC,aAAa,CAAC;MACxG,IAAIa,QAAQ,CAACG,EAAE,EAAE;QACf,MAAME,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClC1B,aAAa,CAACyB,IAAI,CAACA,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,CAACrB,cAAc,CAAC,CAAC;EAEpBwB,SAAS,CAAC,MAAM;IACd,IAAIxB,cAAc,EAAE;MAClBE,iBAAiB,CAAC,CAAC;MACnBqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,cAAc,EAAEE,iBAAiB,EAAEqB,eAAe,CAAC,CAAC;EAIxD,MAAME,sBAAsB,GAAIC,EAAU,IAAK;IAC7CC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBH,EAAE,EAAE;EAC9C,CAAC;EAED,MAAMI,gBAAgB,GAAIxB,IAAY,IAAK;IACzCT,cAAc,CAACS,IAAI,CAAC;EACtB,CAAC;EAED,MAAMyB,qBAAqB,GAAIC,WAAwB,iBACrD/C,OAAA,CAACgD,eAAe;IAEdP,EAAE,EAAEM,WAAW,CAACN,EAAG;IACnBQ,KAAK,EAAEF,WAAW,CAACE,KAAM;IACzBC,SAAS,EAAEH,WAAW,CAACG,SAAS,IAAI,EAAG;IACvCC,QAAQ,EAAEJ,WAAW,CAACI,QAAS;IAC/BC,MAAM,EAAEL,WAAW,CAACK,MAAO;IAC3BjD,OAAO,EAAE4C,WAAW,CAAC5C,OAAQ;IAC7BkD,OAAO,EAAEb;EAAuB,GAP3BO,WAAW,CAACN,EAAE;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQpB,CACF;EAED,oBACEzD,OAAA,CAAC0D,sBAAsB;IACrBC,IAAI,EAAE;MACJV,KAAK,EAAElC,cAAc;MACrB6C,QAAQ,EAAE,mDAAmD;MAC7DC,IAAI,EAAEC,cAAc,CAACC,cAAc,CAAChD,cAAc,CAAC;MACnDiD,eAAe,EAAE;IACnB,CAAE;IACFzD,UAAU,EAAEA,UAAU,GAAG;MACvB0D,KAAK,EAAE1D,UAAU,CAAC2D,iBAAiB;MACnCC,MAAM,EAAE5D,UAAU,CAAC6D,gBAAgB;MACnCC,QAAQ,EAAE9D,UAAU,CAAC+D,kBAAkB;MACvCC,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,kBAAkB;MAC/BC,aAAa,EAAE;IACjB,CAAC,GAAGC,SAAU;IACdC,aAAa,EAAE;MACbC,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAE9D,cAAc;MAC3BQ,KAAK,EAAE;IACT,CAAE;IAAAuD,QAAA,eAEF9E,OAAA,CAAC+E,uBAAuB;MACtBC,KAAK,EAAE5E,YAAa;MACpBK,OAAO,EAAEA,OAAQ;MACjBwE,YAAY,EAAC,2DAA2D;MACxEC,SAAS,EAAC,cAAI;MACdC,UAAU,EAAErC,qBAAsB;MAClCX,UAAU,EAAE;QACVxB,WAAW;QACXE,UAAU;QACVuE,YAAY,EAAEvC;MAChB;IAAE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACoB,CAAC;AAG7B,CAAC;AAACvD,EAAA,CAhHID,aAAuB;EAAA,QACPH,SAAS;AAAA;AAAAuF,EAAA,GADzBpF,aAAuB;AAkH7B,eAAeA,aAAa;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}