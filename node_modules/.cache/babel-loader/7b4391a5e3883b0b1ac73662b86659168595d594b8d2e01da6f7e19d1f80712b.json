{"ast": null, "code": "export default function (interpolator, n) {\n  var samples = new Array(n);\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n  return samples;\n}", "map": {"version": 3, "names": ["interpolator", "n", "samples", "Array", "i"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-interpolate/src/quantize.js"], "sourcesContent": ["export default function(interpolator, n) {\n  var samples = new Array(n);\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n  return samples;\n}\n"], "mappings": "AAAA,eAAe,UAASA,YAAY,EAAEC,CAAC,EAAE;EACvC,IAAIC,OAAO,GAAG,IAAIC,KAAK,CAACF,CAAC,CAAC;EAC1B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,GAAGJ,YAAY,CAACI,CAAC,IAAIH,CAAC,GAAG,CAAC,CAAC,CAAC;EAClE,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}