{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport { formatValue } from \"../../../utils/dateUtil\";\nexport default function useInputProps(props, /** Used for SinglePicker */\npostProps) {\n  var format = props.format,\n    maskFormat = props.maskFormat,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    preserveInvalidOnBlur = props.preserveInvalidOnBlur,\n    inputReadOnly = props.inputReadOnly,\n    required = props.required,\n    ariaRequired = props['aria-required'],\n    onSubmit = props.onSubmit,\n    _onFocus = props.onFocus,\n    _onBlur = props.onBlur,\n    onInputChange = props.onInputChange,\n    onInvalid = props.onInvalid,\n    open = props.open,\n    onOpenChange = props.onOpenChange,\n    _onKeyDown = props.onKeyDown,\n    _onChange = props.onChange,\n    activeHelp = props.activeHelp,\n    name = props.name,\n    autoComplete = props.autoComplete,\n    id = props.id,\n    value = props.value,\n    invalid = props.invalid,\n    placeholder = props.placeholder,\n    disabled = props.disabled,\n    activeIndex = props.activeIndex,\n    allHelp = props.allHelp,\n    picker = props.picker;\n\n  // ======================== Parser ========================\n  var parseDate = function parseDate(str, formatStr) {\n    var parsed = generateConfig.locale.parse(locale.locale, str, [formatStr]);\n    return parsed && generateConfig.isValidate(parsed) ? parsed : null;\n  };\n\n  // ========================= Text =========================\n  var firstFormat = format[0];\n  var getText = React.useCallback(function (date) {\n    return formatValue(date, {\n      locale: locale,\n      format: firstFormat,\n      generateConfig: generateConfig\n    });\n  }, [locale, generateConfig, firstFormat]);\n  var valueTexts = React.useMemo(function () {\n    return value.map(getText);\n  }, [value, getText]);\n\n  // ========================= Size =========================\n  var size = React.useMemo(function () {\n    var defaultSize = picker === 'time' ? 8 : 10;\n    var length = typeof firstFormat === 'function' ? firstFormat(generateConfig.getNow()).length : firstFormat.length;\n    return Math.max(defaultSize, length) + 2;\n  }, [firstFormat, picker, generateConfig]);\n\n  // ======================= Validate =======================\n  var _validateFormat = function validateFormat(text) {\n    for (var i = 0; i < format.length; i += 1) {\n      var singleFormat = format[i];\n\n      // Only support string type\n      if (typeof singleFormat === 'string') {\n        var parsed = parseDate(text, singleFormat);\n        if (parsed) {\n          return parsed;\n        }\n      }\n    }\n    return false;\n  };\n\n  // ======================== Input =========================\n  var getInputProps = function getInputProps(index) {\n    function getProp(propValue) {\n      return index !== undefined ? propValue[index] : propValue;\n    }\n    var pickedAttrs = pickAttrs(props, {\n      aria: true,\n      data: true\n    });\n    var inputProps = _objectSpread(_objectSpread({}, pickedAttrs), {}, {\n      // ============== Shared ==============\n      format: maskFormat,\n      validateFormat: function validateFormat(text) {\n        return !!_validateFormat(text);\n      },\n      preserveInvalidOnBlur: preserveInvalidOnBlur,\n      readOnly: inputReadOnly,\n      required: required,\n      'aria-required': ariaRequired,\n      name: name,\n      autoComplete: autoComplete,\n      size: size,\n      // ============= By Index =============\n      id: getProp(id),\n      value: getProp(valueTexts) || '',\n      invalid: getProp(invalid),\n      placeholder: getProp(placeholder),\n      active: activeIndex === index,\n      helped: allHelp || activeHelp && activeIndex === index,\n      disabled: getProp(disabled),\n      onFocus: function onFocus(event) {\n        _onFocus(event, index);\n      },\n      onBlur: function onBlur(event) {\n        // Blur do not trigger close\n        // Since it may focus to the popup panel\n        _onBlur(event, index);\n      },\n      onSubmit: onSubmit,\n      // Get validate text value\n      onChange: function onChange(text) {\n        onInputChange();\n        var parsed = _validateFormat(text);\n        if (parsed) {\n          onInvalid(false, index);\n          _onChange(parsed, index);\n          return;\n        }\n\n        // Tell outer that the value typed is invalid.\n        // If text is empty, it means valid.\n        onInvalid(!!text, index);\n      },\n      onHelp: function onHelp() {\n        onOpenChange(true, {\n          index: index\n        });\n      },\n      onKeyDown: function onKeyDown(event) {\n        var prevented = false;\n        _onKeyDown === null || _onKeyDown === void 0 || _onKeyDown(event, function () {\n          if (process.env.NODE_ENV !== 'production') {\n            warning(false, '`preventDefault` callback is deprecated. Please call `event.preventDefault` directly.');\n          }\n          prevented = true;\n        });\n        if (!event.defaultPrevented && !prevented) {\n          switch (event.key) {\n            case 'Escape':\n              onOpenChange(false, {\n                index: index\n              });\n              break;\n            case 'Enter':\n              if (!open) {\n                onOpenChange(true);\n              }\n              break;\n          }\n        }\n      }\n    }, postProps === null || postProps === void 0 ? void 0 : postProps({\n      valueTexts: valueTexts\n    }));\n\n    // ============== Clean Up ==============\n    Object.keys(inputProps).forEach(function (key) {\n      if (inputProps[key] === undefined) {\n        delete inputProps[key];\n      }\n    });\n    return inputProps;\n  };\n  return [getInputProps, getText];\n}", "map": {"version": 3, "names": ["_objectSpread", "warning", "pickAttrs", "React", "formatValue", "useInputProps", "props", "postProps", "format", "maskFormat", "generateConfig", "locale", "preserveInvalidOnBlur", "inputReadOnly", "required", "ariaRequired", "onSubmit", "_onFocus", "onFocus", "_onBlur", "onBlur", "onInputChange", "onInvalid", "open", "onOpenChange", "_onKeyDown", "onKeyDown", "_onChange", "onChange", "activeHelp", "name", "autoComplete", "id", "value", "invalid", "placeholder", "disabled", "activeIndex", "allHelp", "picker", "parseDate", "str", "formatStr", "parsed", "parse", "isValidate", "firstFormat", "getText", "useCallback", "date", "valueTexts", "useMemo", "map", "size", "defaultSize", "length", "getNow", "Math", "max", "_validateFormat", "validateFormat", "text", "i", "singleFormat", "getInputProps", "index", "getProp", "propValue", "undefined", "pickedAttrs", "aria", "data", "inputProps", "readOnly", "active", "helped", "event", "onHelp", "prevented", "process", "env", "NODE_ENV", "defaultPrevented", "key", "Object", "keys", "for<PERSON>ach"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-picker/es/PickerInput/Selector/hooks/useInputProps.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport { formatValue } from \"../../../utils/dateUtil\";\nexport default function useInputProps(props, /** Used for SinglePicker */\npostProps) {\n  var format = props.format,\n    maskFormat = props.maskFormat,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    preserveInvalidOnBlur = props.preserveInvalidOnBlur,\n    inputReadOnly = props.inputReadOnly,\n    required = props.required,\n    ariaRequired = props['aria-required'],\n    onSubmit = props.onSubmit,\n    _onFocus = props.onFocus,\n    _onBlur = props.onBlur,\n    onInputChange = props.onInputChange,\n    onInvalid = props.onInvalid,\n    open = props.open,\n    onOpenChange = props.onOpenChange,\n    _onKeyDown = props.onKeyDown,\n    _onChange = props.onChange,\n    activeHelp = props.activeHelp,\n    name = props.name,\n    autoComplete = props.autoComplete,\n    id = props.id,\n    value = props.value,\n    invalid = props.invalid,\n    placeholder = props.placeholder,\n    disabled = props.disabled,\n    activeIndex = props.activeIndex,\n    allHelp = props.allHelp,\n    picker = props.picker;\n\n  // ======================== Parser ========================\n  var parseDate = function parseDate(str, formatStr) {\n    var parsed = generateConfig.locale.parse(locale.locale, str, [formatStr]);\n    return parsed && generateConfig.isValidate(parsed) ? parsed : null;\n  };\n\n  // ========================= Text =========================\n  var firstFormat = format[0];\n  var getText = React.useCallback(function (date) {\n    return formatValue(date, {\n      locale: locale,\n      format: firstFormat,\n      generateConfig: generateConfig\n    });\n  }, [locale, generateConfig, firstFormat]);\n  var valueTexts = React.useMemo(function () {\n    return value.map(getText);\n  }, [value, getText]);\n\n  // ========================= Size =========================\n  var size = React.useMemo(function () {\n    var defaultSize = picker === 'time' ? 8 : 10;\n    var length = typeof firstFormat === 'function' ? firstFormat(generateConfig.getNow()).length : firstFormat.length;\n    return Math.max(defaultSize, length) + 2;\n  }, [firstFormat, picker, generateConfig]);\n\n  // ======================= Validate =======================\n  var _validateFormat = function validateFormat(text) {\n    for (var i = 0; i < format.length; i += 1) {\n      var singleFormat = format[i];\n\n      // Only support string type\n      if (typeof singleFormat === 'string') {\n        var parsed = parseDate(text, singleFormat);\n        if (parsed) {\n          return parsed;\n        }\n      }\n    }\n    return false;\n  };\n\n  // ======================== Input =========================\n  var getInputProps = function getInputProps(index) {\n    function getProp(propValue) {\n      return index !== undefined ? propValue[index] : propValue;\n    }\n    var pickedAttrs = pickAttrs(props, {\n      aria: true,\n      data: true\n    });\n    var inputProps = _objectSpread(_objectSpread({}, pickedAttrs), {}, {\n      // ============== Shared ==============\n      format: maskFormat,\n      validateFormat: function validateFormat(text) {\n        return !!_validateFormat(text);\n      },\n      preserveInvalidOnBlur: preserveInvalidOnBlur,\n      readOnly: inputReadOnly,\n      required: required,\n      'aria-required': ariaRequired,\n      name: name,\n      autoComplete: autoComplete,\n      size: size,\n      // ============= By Index =============\n      id: getProp(id),\n      value: getProp(valueTexts) || '',\n      invalid: getProp(invalid),\n      placeholder: getProp(placeholder),\n      active: activeIndex === index,\n      helped: allHelp || activeHelp && activeIndex === index,\n      disabled: getProp(disabled),\n      onFocus: function onFocus(event) {\n        _onFocus(event, index);\n      },\n      onBlur: function onBlur(event) {\n        // Blur do not trigger close\n        // Since it may focus to the popup panel\n        _onBlur(event, index);\n      },\n      onSubmit: onSubmit,\n      // Get validate text value\n      onChange: function onChange(text) {\n        onInputChange();\n        var parsed = _validateFormat(text);\n        if (parsed) {\n          onInvalid(false, index);\n          _onChange(parsed, index);\n          return;\n        }\n\n        // Tell outer that the value typed is invalid.\n        // If text is empty, it means valid.\n        onInvalid(!!text, index);\n      },\n      onHelp: function onHelp() {\n        onOpenChange(true, {\n          index: index\n        });\n      },\n      onKeyDown: function onKeyDown(event) {\n        var prevented = false;\n        _onKeyDown === null || _onKeyDown === void 0 || _onKeyDown(event, function () {\n          if (process.env.NODE_ENV !== 'production') {\n            warning(false, '`preventDefault` callback is deprecated. Please call `event.preventDefault` directly.');\n          }\n          prevented = true;\n        });\n        if (!event.defaultPrevented && !prevented) {\n          switch (event.key) {\n            case 'Escape':\n              onOpenChange(false, {\n                index: index\n              });\n              break;\n            case 'Enter':\n              if (!open) {\n                onOpenChange(true);\n              }\n              break;\n          }\n        }\n      }\n    }, postProps === null || postProps === void 0 ? void 0 : postProps({\n      valueTexts: valueTexts\n    }));\n\n    // ============== Clean Up ==============\n    Object.keys(inputProps).forEach(function (key) {\n      if (inputProps[key] === undefined) {\n        delete inputProps[key];\n      }\n    });\n    return inputProps;\n  };\n  return [getInputProps, getText];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;AAC7CC,SAAS,EAAE;EACT,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACvBC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,qBAAqB,GAAGN,KAAK,CAACM,qBAAqB;IACnDC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,YAAY,GAAGT,KAAK,CAAC,eAAe,CAAC;IACrCU,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,QAAQ,GAAGX,KAAK,CAACY,OAAO;IACxBC,OAAO,GAAGb,KAAK,CAACc,MAAM;IACtBC,aAAa,GAAGf,KAAK,CAACe,aAAa;IACnCC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,IAAI,GAAGjB,KAAK,CAACiB,IAAI;IACjBC,YAAY,GAAGlB,KAAK,CAACkB,YAAY;IACjCC,UAAU,GAAGnB,KAAK,CAACoB,SAAS;IAC5BC,SAAS,GAAGrB,KAAK,CAACsB,QAAQ;IAC1BC,UAAU,GAAGvB,KAAK,CAACuB,UAAU;IAC7BC,IAAI,GAAGxB,KAAK,CAACwB,IAAI;IACjBC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,EAAE,GAAG1B,KAAK,CAAC0B,EAAE;IACbC,KAAK,GAAG3B,KAAK,CAAC2B,KAAK;IACnBC,OAAO,GAAG5B,KAAK,CAAC4B,OAAO;IACvBC,WAAW,GAAG7B,KAAK,CAAC6B,WAAW;IAC/BC,QAAQ,GAAG9B,KAAK,CAAC8B,QAAQ;IACzBC,WAAW,GAAG/B,KAAK,CAAC+B,WAAW;IAC/BC,OAAO,GAAGhC,KAAK,CAACgC,OAAO;IACvBC,MAAM,GAAGjC,KAAK,CAACiC,MAAM;;EAEvB;EACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,GAAG,EAAEC,SAAS,EAAE;IACjD,IAAIC,MAAM,GAAGjC,cAAc,CAACC,MAAM,CAACiC,KAAK,CAACjC,MAAM,CAACA,MAAM,EAAE8B,GAAG,EAAE,CAACC,SAAS,CAAC,CAAC;IACzE,OAAOC,MAAM,IAAIjC,cAAc,CAACmC,UAAU,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,IAAI;EACpE,CAAC;;EAED;EACA,IAAIG,WAAW,GAAGtC,MAAM,CAAC,CAAC,CAAC;EAC3B,IAAIuC,OAAO,GAAG5C,KAAK,CAAC6C,WAAW,CAAC,UAAUC,IAAI,EAAE;IAC9C,OAAO7C,WAAW,CAAC6C,IAAI,EAAE;MACvBtC,MAAM,EAAEA,MAAM;MACdH,MAAM,EAAEsC,WAAW;MACnBpC,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACC,MAAM,EAAED,cAAc,EAAEoC,WAAW,CAAC,CAAC;EACzC,IAAII,UAAU,GAAG/C,KAAK,CAACgD,OAAO,CAAC,YAAY;IACzC,OAAOlB,KAAK,CAACmB,GAAG,CAACL,OAAO,CAAC;EAC3B,CAAC,EAAE,CAACd,KAAK,EAAEc,OAAO,CAAC,CAAC;;EAEpB;EACA,IAAIM,IAAI,GAAGlD,KAAK,CAACgD,OAAO,CAAC,YAAY;IACnC,IAAIG,WAAW,GAAGf,MAAM,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE;IAC5C,IAAIgB,MAAM,GAAG,OAAOT,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACpC,cAAc,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACD,MAAM,GAAGT,WAAW,CAACS,MAAM;IACjH,OAAOE,IAAI,CAACC,GAAG,CAACJ,WAAW,EAAEC,MAAM,CAAC,GAAG,CAAC;EAC1C,CAAC,EAAE,CAACT,WAAW,EAAEP,MAAM,EAAE7B,cAAc,CAAC,CAAC;;EAEzC;EACA,IAAIiD,eAAe,GAAG,SAASC,cAAcA,CAACC,IAAI,EAAE;IAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,MAAM,CAAC+C,MAAM,EAAEO,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIC,YAAY,GAAGvD,MAAM,CAACsD,CAAC,CAAC;;MAE5B;MACA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;QACpC,IAAIpB,MAAM,GAAGH,SAAS,CAACqB,IAAI,EAAEE,YAAY,CAAC;QAC1C,IAAIpB,MAAM,EAAE;UACV,OAAOA,MAAM;QACf;MACF;IACF;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,IAAIqB,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,SAASC,OAAOA,CAACC,SAAS,EAAE;MAC1B,OAAOF,KAAK,KAAKG,SAAS,GAAGD,SAAS,CAACF,KAAK,CAAC,GAAGE,SAAS;IAC3D;IACA,IAAIE,WAAW,GAAGnE,SAAS,CAACI,KAAK,EAAE;MACjCgE,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,IAAIC,UAAU,GAAGxE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MACjE;MACA7D,MAAM,EAAEC,UAAU;MAClBmD,cAAc,EAAE,SAASA,cAAcA,CAACC,IAAI,EAAE;QAC5C,OAAO,CAAC,CAACF,eAAe,CAACE,IAAI,CAAC;MAChC,CAAC;MACDjD,qBAAqB,EAAEA,qBAAqB;MAC5C6D,QAAQ,EAAE5D,aAAa;MACvBC,QAAQ,EAAEA,QAAQ;MAClB,eAAe,EAAEC,YAAY;MAC7Be,IAAI,EAAEA,IAAI;MACVC,YAAY,EAAEA,YAAY;MAC1BsB,IAAI,EAAEA,IAAI;MACV;MACArB,EAAE,EAAEkC,OAAO,CAAClC,EAAE,CAAC;MACfC,KAAK,EAAEiC,OAAO,CAAChB,UAAU,CAAC,IAAI,EAAE;MAChChB,OAAO,EAAEgC,OAAO,CAAChC,OAAO,CAAC;MACzBC,WAAW,EAAE+B,OAAO,CAAC/B,WAAW,CAAC;MACjCuC,MAAM,EAAErC,WAAW,KAAK4B,KAAK;MAC7BU,MAAM,EAAErC,OAAO,IAAIT,UAAU,IAAIQ,WAAW,KAAK4B,KAAK;MACtD7B,QAAQ,EAAE8B,OAAO,CAAC9B,QAAQ,CAAC;MAC3BlB,OAAO,EAAE,SAASA,OAAOA,CAAC0D,KAAK,EAAE;QAC/B3D,QAAQ,CAAC2D,KAAK,EAAEX,KAAK,CAAC;MACxB,CAAC;MACD7C,MAAM,EAAE,SAASA,MAAMA,CAACwD,KAAK,EAAE;QAC7B;QACA;QACAzD,OAAO,CAACyD,KAAK,EAAEX,KAAK,CAAC;MACvB,CAAC;MACDjD,QAAQ,EAAEA,QAAQ;MAClB;MACAY,QAAQ,EAAE,SAASA,QAAQA,CAACiC,IAAI,EAAE;QAChCxC,aAAa,CAAC,CAAC;QACf,IAAIsB,MAAM,GAAGgB,eAAe,CAACE,IAAI,CAAC;QAClC,IAAIlB,MAAM,EAAE;UACVrB,SAAS,CAAC,KAAK,EAAE2C,KAAK,CAAC;UACvBtC,SAAS,CAACgB,MAAM,EAAEsB,KAAK,CAAC;UACxB;QACF;;QAEA;QACA;QACA3C,SAAS,CAAC,CAAC,CAACuC,IAAI,EAAEI,KAAK,CAAC;MAC1B,CAAC;MACDY,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;QACxBrD,YAAY,CAAC,IAAI,EAAE;UACjByC,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC;MACDvC,SAAS,EAAE,SAASA,SAASA,CAACkD,KAAK,EAAE;QACnC,IAAIE,SAAS,GAAG,KAAK;QACrBrD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACmD,KAAK,EAAE,YAAY;UAC5E,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzChF,OAAO,CAAC,KAAK,EAAE,uFAAuF,CAAC;UACzG;UACA6E,SAAS,GAAG,IAAI;QAClB,CAAC,CAAC;QACF,IAAI,CAACF,KAAK,CAACM,gBAAgB,IAAI,CAACJ,SAAS,EAAE;UACzC,QAAQF,KAAK,CAACO,GAAG;YACf,KAAK,QAAQ;cACX3D,YAAY,CAAC,KAAK,EAAE;gBAClByC,KAAK,EAAEA;cACT,CAAC,CAAC;cACF;YACF,KAAK,OAAO;cACV,IAAI,CAAC1C,IAAI,EAAE;gBACTC,YAAY,CAAC,IAAI,CAAC;cACpB;cACA;UACJ;QACF;MACF;IACF,CAAC,EAAEjB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC;MACjE2C,UAAU,EAAEA;IACd,CAAC,CAAC,CAAC;;IAEH;IACAkC,MAAM,CAACC,IAAI,CAACb,UAAU,CAAC,CAACc,OAAO,CAAC,UAAUH,GAAG,EAAE;MAC7C,IAAIX,UAAU,CAACW,GAAG,CAAC,KAAKf,SAAS,EAAE;QACjC,OAAOI,UAAU,CAACW,GAAG,CAAC;MACxB;IACF,CAAC,CAAC;IACF,OAAOX,UAAU;EACnB,CAAC;EACD,OAAO,CAACR,aAAa,EAAEjB,OAAO,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}