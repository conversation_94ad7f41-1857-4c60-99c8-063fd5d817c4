{"ast": null, "code": "import { min, sqrt } from \"../math.js\";\nexport default {\n  draw(context, size) {\n    const r = sqrt(size - min(size / 6, 1.7)) * 0.6189;\n    context.moveTo(-r, -r);\n    context.lineTo(r, r);\n    context.moveTo(-r, r);\n    context.lineTo(r, -r);\n  }\n};", "map": {"version": 3, "names": ["min", "sqrt", "draw", "context", "size", "r", "moveTo", "lineTo"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-shape/src/symbol/times.js"], "sourcesContent": ["import {min, sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size - min(size / 6, 1.7)) * 0.6189;\n    context.moveTo(-r, -r);\n    context.lineTo(r, r);\n    context.moveTo(-r, r);\n    context.lineTo(r, -r);\n  }\n};\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,QAAO,YAAY;AAEpC,eAAe;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGJ,IAAI,CAACG,IAAI,GAAGJ,GAAG,CAACI,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM;IAClDD,OAAO,CAACG,MAAM,CAAC,CAACD,CAAC,EAAE,CAACA,CAAC,CAAC;IACtBF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAEA,CAAC,CAAC;IACpBF,OAAO,CAACG,MAAM,CAAC,CAACD,CAAC,EAAEA,CAAC,CAAC;IACrBF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAE,CAACA,CAAC,CAAC;EACvB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}