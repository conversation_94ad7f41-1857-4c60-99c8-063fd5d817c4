{"ast": null, "code": "// ====================== Mode ======================\nexport function getRealPlacement(placement, rtl) {\n  if (placement !== undefined) {\n    return placement;\n  }\n  return rtl ? 'bottomRight' : 'bottomLeft';\n}", "map": {"version": 3, "names": ["getRealPlacement", "placement", "rtl", "undefined"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-picker/es/utils/uiUtil.js"], "sourcesContent": ["// ====================== Mode ======================\nexport function getRealPlacement(placement, rtl) {\n  if (placement !== undefined) {\n    return placement;\n  }\n  return rtl ? 'bottomRight' : 'bottomLeft';\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,GAAG,EAAE;EAC/C,IAAID,SAAS,KAAKE,SAAS,EAAE;IAC3B,OAAOF,SAAS;EAClB;EACA,OAAOC,GAAG,GAAG,aAAa,GAAG,YAAY;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}