{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { useLocation } from 'react-router-dom';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport AdPlacement from '../components/AdPlacement';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Scholarships = () => {\n  _s();\n  const location = useLocation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('');\n  const [selectedCountry, setSelectedCountry] = useState('');\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    page: 1,\n    limit: 9,\n    // Show 9 scholarships per page (3x3 grid)\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Read URL parameters on component mount\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const levelParam = searchParams.get('level');\n    const countryParam = searchParams.get('country');\n    if (levelParam) {\n      setSelectedLevel(levelParam);\n    }\n    if (countryParam) {\n      setSelectedCountry(countryParam);\n    }\n  }, [location.search]);\n\n  // Fetch scholarships with pagination and filters\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      params.append('page', pagination.page.toString());\n      params.append('limit', pagination.limit.toString());\n      if (searchQuery) {\n        params.append('q', searchQuery);\n      }\n      if (selectedLevel) {\n        params.append('level', selectedLevel);\n      }\n      if (selectedCountry) {\n        params.append('country', selectedCountry);\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      console.log('Scholarships search API response:', data);\n\n      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }\n      const scholarshipsData = data.data || data.scholarships || [];\n      const paginationData = data.pagination || {};\n      setScholarships(scholarshipsData);\n      setPagination(paginationData || {\n        total: scholarshipsData.length || 0,\n        page: 1,\n        limit: 9,\n        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),\n        hasNextPage: false,\n        hasPreviousPage: false\n      });\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching scholarships:', err);\n      setError('Failed to load scholarships. Please try again later.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle page change\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    // Scroll to top when page changes\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-br from-blue-900 via-primary-dark to-primary overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto py-20 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6 animate-fade-in\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), pagination.total, \" bourses disponibles\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in\",\n            children: [\"Trouvez Votre\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block text-yellow-300\",\n              children: \"Bourse Id\\xE9ale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-5 max-w-2xl mx-auto text-xl text-white/80 animate-slide-up\",\n            children: \"Acc\\xE9dez \\xE0 la plus grande base de donn\\xE9es de bourses d'\\xE9tudes au monde. Des opportunit\\xE9s de financement pour tous les niveaux d'\\xE9tudes, dans tous les pays, pour r\\xE9aliser vos r\\xEAves acad\\xE9miques sans contraintes financi\\xE8res.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12 animate-fade-in\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                children: \"1000+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-100\",\n                children: \"Bourses Actives\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                children: \"60+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-100\",\n                children: \"Pays Couverts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                children: \"24/7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-100\",\n                children: \"Mise \\xE0 Jour\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block\",\n      children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n        adSlot: \"1234567890\",\n        adSize: \"leaderboard\",\n        responsive: true,\n        fullWidth: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-xl rounded-2xl p-8 mb-12 transform translate-y-0 animate-slide-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900\",\n            children: \"Filtrer les Bourses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setSearchQuery('');\n              setSelectedLevel('');\n              setSelectedCountry('');\n              setPagination(prev => ({\n                ...prev,\n                page: 1\n              }));\n            },\n            className: \"text-sm text-primary hover:text-primary-dark font-medium flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), \"R\\xE9initialiser les filtres\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Recherche\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"search\",\n                id: \"search\",\n                value: searchQuery,\n                onChange: e => {\n                  setSearchQuery(e.target.value);\n                  setPagination(prev => ({\n                    ...prev,\n                    page: 1\n                  }));\n                },\n                className: \"pl-10 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\",\n                placeholder: \"Rechercher des bourses...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"level\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Niveau d'\\xC9tudes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"level\",\n              name: \"level\",\n              value: selectedLevel,\n              onChange: e => {\n                setSelectedLevel(e.target.value);\n                setPagination(prev => ({\n                  ...prev,\n                  page: 1\n                }));\n              },\n              className: \"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les niveaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Licence\",\n                children: \"Licence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Master\",\n                children: \"Master\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Doctorat\",\n                children: \"Doctorat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Post-doctorat\",\n                children: \"Post-doctorat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"country\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Pays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"country\",\n              name: \"country\",\n              value: selectedCountry,\n              onChange: e => {\n                setSelectedCountry(e.target.value);\n                setPagination(prev => ({\n                  ...prev,\n                  page: 1\n                }));\n              },\n              className: \"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les pays\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"France\",\n                children: \"France\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Canada\",\n                children: \"Canada\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Belgique\",\n                children: \"Belgique\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Suisse\",\n                children: \"Suisse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Maroc\",\n                children: \"Maroc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Tunisie\",\n                children: \"Tunisie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"S\\xE9n\\xE9gal\",\n                children: \"S\\xE9n\\xE9gal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"C\\xF4te d'Ivoire\",\n                children: \"C\\xF4te d'Ivoire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"status\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"status\",\n              name: \"status\",\n              className: \"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les statuts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"open\",\n                children: \"Ouvertes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"closed\",\n                children: \"Ferm\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"urgent\",\n                children: \"Urgentes (< 7 jours)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-2/3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                children: \"R\\xE9sultats de recherche\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: !loading && !error && `${pagination.total} bourses trouvées`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 md:mt-0\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"recent\",\n                  children: \"Plus r\\xE9centes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"deadline\",\n                  children: \"Date limite proche\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"relevance\",\n                  children: \"Pertinence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 9\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center py-16\",\n            children: /*#__PURE__*/_jsxDEV(Spin, {\n              size: \"large\",\n              tip: \"Chargement des bourses...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 11\n          }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"Erreur\",\n            description: error,\n            type: \"error\",\n            showIcon: true,\n            className: \"mb-6 rounded-xl shadow-md\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 11\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\",\n              children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                  id: scholarship.id,\n                  title: scholarship.title,\n                  thumbnail: scholarship.thumbnail,\n                  deadline: scholarship.deadline,\n                  isOpen: scholarship.isOpen,\n                  level: scholarship.level,\n                  country: scholarship.country,\n                  onClick: (id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`,\n                  index: index\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)\n              }, scholarship.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 13\n            }, this), scholarships.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"mx-auto h-12 w-12 text-gray-400\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 1.5,\n                  d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-4 text-lg font-medium text-gray-900\",\n                children: \"Aucune bourse trouv\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-500 max-w-md mx-auto\",\n                children: \"Essayez d'ajuster vos crit\\xE8res de recherche ou de filtrage pour trouver ce que vous cherchez.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchQuery('');\n                  setSelectedLevel('');\n                  setSelectedCountry('');\n                  setPagination(prev => ({\n                    ...prev,\n                    page: 1\n                  }));\n                },\n                className: \"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                children: \"R\\xE9initialiser les filtres\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mt-12\",\n              children: /*#__PURE__*/_jsxDEV(Pagination, {\n                current: pagination.page,\n                total: pagination.total,\n                pageSize: pagination.limit,\n                onChange: handlePageChange,\n                showSizeChanger: false,\n                showQuickJumper: true,\n                showTotal: total => `Total ${total} bourses`,\n                className: \"shadow-sm rounded-xl p-2 bg-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProfessionalSidebar, {\n          config: {\n            type: 'levels',\n            currentItem: selectedLevel || undefined,\n            limit: 10\n          },\n          className: \"lg:w-1/3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageEndSuggestions, {\n      currentPageType: \"scholarship\",\n      currentItem: selectedLevel || selectedCountry\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(Scholarships, \"6ePyox+ZYk0YOUy0AN9IK1VUS44=\", false, function () {\n  return [useLocation];\n});\n_c = Scholarships;\nexport default Scholarships;\nvar _c;\n$RefreshReg$(_c, \"Scholarships\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Pagination", "Spin", "<PERSON><PERSON>", "useLocation", "EnhancedScholarshipCard", "ProfessionalSidebar", "PageEndSuggestions", "AdPlacement", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Scholarships", "_s", "location", "searchQuery", "setSearch<PERSON>uery", "selectedLevel", "setSelectedLevel", "selectedCountry", "setSelectedCountry", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "total", "page", "limit", "totalPages", "hasNextPage", "hasPreviousPage", "searchParams", "URLSearchParams", "search", "levelParam", "get", "countryParam", "fetchScholarships", "params", "append", "toString", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "data", "json", "console", "log", "scholarshipsData", "paginationData", "length", "Math", "ceil", "err", "handlePageChange", "prev", "window", "scrollTo", "top", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "adSlot", "adSize", "responsive", "fullWidth", "onClick", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "htmlFor", "type", "name", "id", "value", "onChange", "e", "target", "placeholder", "size", "tip", "message", "description", "showIcon", "map", "scholarship", "index", "style", "animationDelay", "title", "thumbnail", "deadline", "isOpen", "level", "country", "slug", "href", "current", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "config", "currentItem", "undefined", "currentPageType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { useLocation } from 'react-router-dom';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport AdPlacement from '../components/AdPlacement';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  country: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail: string;\n}\n\ninterface PaginationData {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nconst Scholarships: React.FC = () => {\n  const location = useLocation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('');\n  const [selectedCountry, setSelectedCountry] = useState('');\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pagination, setPagination] = useState<PaginationData>({\n    total: 0,\n    page: 1,\n    limit: 9, // Show 9 scholarships per page (3x3 grid)\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Read URL parameters on component mount\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const levelParam = searchParams.get('level');\n    const countryParam = searchParams.get('country');\n\n    if (levelParam) {\n      setSelectedLevel(levelParam);\n    }\n    if (countryParam) {\n      setSelectedCountry(countryParam);\n    }\n  }, [location.search]);\n\n  // Fetch scholarships with pagination and filters\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      params.append('page', pagination.page.toString());\n      params.append('limit', pagination.limit.toString());\n\n      if (searchQuery) {\n        params.append('q', searchQuery);\n      }\n\n      if (selectedLevel) {\n        params.append('level', selectedLevel);\n      }\n\n      if (selectedCountry) {\n        params.append('country', selectedCountry);\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      console.log('Scholarships search API response:', data);\n\n      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }\n      const scholarshipsData = data.data || data.scholarships || [];\n      const paginationData = data.pagination || {};\n\n      setScholarships(scholarshipsData);\n      setPagination(paginationData || {\n        total: scholarshipsData.length || 0,\n        page: 1,\n        limit: 9,\n        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),\n        hasNextPage: false,\n        hasPreviousPage: false\n      });\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching scholarships:', err);\n      setError('Failed to load scholarships. Please try again later.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle page change\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    // Scroll to top when page changes\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"bg-white min-h-screen\">\n      {/* Hero Section */}\n      <div className=\"relative bg-gradient-to-br from-blue-900 via-primary-dark to-primary overflow-hidden\">\n        {/* Background overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply\" />\n\n        <div className=\"max-w-7xl mx-auto py-20 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10\">\n          <div className=\"text-center\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6 animate-fade-in\">\n              <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n              {pagination.total} bourses disponibles\n            </div>\n\n            <h1 className=\"text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in\">\n              Trouvez Votre\n              <span className=\"block text-yellow-300\">Bourse Idéale</span>\n            </h1>\n\n            <p className=\"mt-5 max-w-2xl mx-auto text-xl text-white/80 animate-slide-up\">\n              Accédez à la plus grande base de données de bourses d'études au monde.\n              Des opportunités de financement pour tous les niveaux d'études, dans tous les pays,\n              pour réaliser vos rêves académiques sans contraintes financières.\n            </p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12 animate-fade-in\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                <div className=\"text-3xl font-bold text-yellow-300 mb-2\">1000+</div>\n                <div className=\"text-blue-100\">Bourses Actives</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                <div className=\"text-3xl font-bold text-yellow-300 mb-2\">60+</div>\n                <div className=\"text-blue-100\">Pays Couverts</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                <div className=\"text-3xl font-bold text-yellow-300 mb-2\">24/7</div>\n                <div className=\"text-blue-100\">Mise à Jour</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Ad Placement - Top Banner */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block\">\n        <AdPlacement\n          adSlot=\"1234567890\"\n          adSize=\"leaderboard\"\n          responsive={true}\n          fullWidth={true}\n        />\n      </div>\n\n      {/* Search and Filter Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10\">\n        <div className=\"bg-white shadow-xl rounded-2xl p-8 mb-12 transform translate-y-0 animate-slide-up\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-bold text-gray-900\">Filtrer les Bourses</h2>\n            <button\n              onClick={() => {\n                setSearchQuery('');\n                setSelectedLevel('');\n                setSelectedCountry('');\n                setPagination(prev => ({ ...prev, page: 1 }));\n              }}\n              className=\"text-sm text-primary hover:text-primary-dark font-medium flex items-center\"\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n              </svg>\n              Réinitialiser les filtres\n            </button>\n          </div>\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n            <div>\n              <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700\">\n                Recherche\n              </label>\n              <div className=\"mt-1 relative rounded-md shadow-sm\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <input\n                  type=\"text\"\n                  name=\"search\"\n                  id=\"search\"\n                  value={searchQuery}\n                  onChange={(e) => {\n                    setSearchQuery(e.target.value);\n                    setPagination(prev => ({ ...prev, page: 1 }));\n                  }}\n                  className=\"pl-10 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\n                  placeholder=\"Rechercher des bourses...\"\n                />\n              </div>\n            </div>\n            <div>\n              <label htmlFor=\"level\" className=\"block text-sm font-medium text-gray-700\">\n                Niveau d'Études\n              </label>\n              <select\n                id=\"level\"\n                name=\"level\"\n                value={selectedLevel}\n                onChange={(e) => {\n                  setSelectedLevel(e.target.value);\n                  setPagination(prev => ({ ...prev, page: 1 }));\n                }}\n                className=\"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\"\n              >\n                <option value=\"\">Tous les niveaux</option>\n                <option value=\"Licence\">Licence</option>\n                <option value=\"Master\">Master</option>\n                <option value=\"Doctorat\">Doctorat</option>\n                <option value=\"Post-doctorat\">Post-doctorat</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"country\" className=\"block text-sm font-medium text-gray-700\">\n                Pays\n              </label>\n              <select\n                id=\"country\"\n                name=\"country\"\n                value={selectedCountry}\n                onChange={(e) => {\n                  setSelectedCountry(e.target.value);\n                  setPagination(prev => ({ ...prev, page: 1 }));\n                }}\n                className=\"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\"\n              >\n                <option value=\"\">Tous les pays</option>\n                <option value=\"France\">France</option>\n                <option value=\"Canada\">Canada</option>\n                <option value=\"Belgique\">Belgique</option>\n                <option value=\"Suisse\">Suisse</option>\n                <option value=\"Maroc\">Maroc</option>\n                <option value=\"Tunisie\">Tunisie</option>\n                <option value=\"Sénégal\">Sénégal</option>\n                <option value=\"Côte d'Ivoire\">Côte d'Ivoire</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700\">\n                Statut\n              </label>\n              <select\n                id=\"status\"\n                name=\"status\"\n                className=\"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\"\n              >\n                <option value=\"\">Tous les statuts</option>\n                <option value=\"open\">Ouvertes</option>\n                <option value=\"closed\">Fermées</option>\n                <option value=\"urgent\">Urgentes (&lt; 7 jours)</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:w-2/3\">\n        {/* Results header */}\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Résultats de recherche</h2>\n            <p className=\"text-gray-600\">\n              {!loading && !error && `${pagination.total} bourses trouvées`}\n            </p>\n          </div>\n          <div className=\"mt-4 md:mt-0\">\n            <select\n              className=\"rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm\"\n            >\n              <option value=\"recent\">Plus récentes</option>\n              <option value=\"deadline\">Date limite proche</option>\n              <option value=\"relevance\">Pertinence</option>\n            </select>\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-16\">\n            <Spin size=\"large\" tip=\"Chargement des bourses...\" />\n          </div>\n        ) : error ? (\n          <Alert\n            message=\"Erreur\"\n            description={error}\n            type=\"error\"\n            showIcon\n            className=\"mb-6 rounded-xl shadow-md\"\n          />\n        ) : (\n          <>\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n              {scholarships.map((scholarship, index) => (\n                <div key={scholarship.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                  <EnhancedScholarshipCard\n                    id={scholarship.id}\n                    title={scholarship.title}\n                    thumbnail={scholarship.thumbnail}\n                    deadline={scholarship.deadline}\n                    isOpen={scholarship.isOpen}\n                    level={scholarship.level}\n                    country={scholarship.country}\n                    onClick={(id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`}\n                    index={index}\n                  />\n                </div>\n              ))}\n            </div>\n\n            {/* No Results Message */}\n            {scholarships.length === 0 && (\n              <div className=\"text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100\">\n                <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <h3 className=\"mt-4 text-lg font-medium text-gray-900\">Aucune bourse trouvée</h3>\n                <p className=\"mt-2 text-sm text-gray-500 max-w-md mx-auto\">\n                  Essayez d'ajuster vos critères de recherche ou de filtrage pour trouver ce que vous cherchez.\n                </p>\n                <button\n                  onClick={() => {\n                    setSearchQuery('');\n                    setSelectedLevel('');\n                    setSelectedCountry('');\n                    setPagination(prev => ({ ...prev, page: 1 }));\n                  }}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n                >\n                  Réinitialiser les filtres\n                </button>\n              </div>\n            )}\n\n            {/* Pagination */}\n            {pagination.total > 0 && (\n              <div className=\"flex justify-center mt-12\">\n                <Pagination\n                  current={pagination.page}\n                  total={pagination.total}\n                  pageSize={pagination.limit}\n                  onChange={handlePageChange}\n                  showSizeChanger={false}\n                  showQuickJumper\n                  showTotal={(total) => `Total ${total} bourses`}\n                  className=\"shadow-sm rounded-xl p-2 bg-white\"\n                />\n              </div>\n            )}\n          </>\n        )}\n            </div>\n\n            {/* Sidebar */}\n            <ProfessionalSidebar\n              config={{\n                type: 'levels' as const,\n                currentItem: selectedLevel || undefined,\n                limit: 10\n              }}\n              className=\"lg:w-1/3\"\n            />\n          </div>\n        </div>\n\n        {/* Page End Suggestions */}\n        <PageEndSuggestions\n          currentPageType=\"scholarship\"\n          currentItem={selectedLevel || selectedCountry}\n        />\n      </div>\n    );\n};\n\nexport default Scholarships;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsBpD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAiB;IAC3D+B,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IAAE;IACVC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMoC,YAAY,GAAG,IAAIC,eAAe,CAACtB,QAAQ,CAACuB,MAAM,CAAC;IACzD,MAAMC,UAAU,GAAGH,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IAC5C,MAAMC,YAAY,GAAGL,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC;IAEhD,IAAID,UAAU,EAAE;MACdpB,gBAAgB,CAACoB,UAAU,CAAC;IAC9B;IACA,IAAIE,YAAY,EAAE;MAChBpB,kBAAkB,CAACoB,YAAY,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1B,QAAQ,CAACuB,MAAM,CAAC,CAAC;;EAErB;EACAtC,SAAS,CAAC,MAAM;IACd0C,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACd,UAAU,CAACG,IAAI,EAAEb,aAAa,EAAEE,eAAe,EAAEJ,WAAW,CAAC,CAAC;EAElE,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMkB,MAAM,GAAG,IAAIN,eAAe,CAAC,CAAC;MACpCM,MAAM,CAACC,MAAM,CAAC,MAAM,EAAEhB,UAAU,CAACG,IAAI,CAACc,QAAQ,CAAC,CAAC,CAAC;MACjDF,MAAM,CAACC,MAAM,CAAC,OAAO,EAAEhB,UAAU,CAACI,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;MAEnD,IAAI7B,WAAW,EAAE;QACf2B,MAAM,CAACC,MAAM,CAAC,GAAG,EAAE5B,WAAW,CAAC;MACjC;MAEA,IAAIE,aAAa,EAAE;QACjByB,MAAM,CAACC,MAAM,CAAC,OAAO,EAAE1B,aAAa,CAAC;MACvC;MAEA,IAAIE,eAAe,EAAE;QACnBuB,MAAM,CAACC,MAAM,CAAC,SAAS,EAAExB,eAAe,CAAC;MAC3C;MAEA,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,4BAA4BP,MAAM,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC;MAExI,IAAI,CAACC,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;MAEtD;MACA,MAAMI,gBAAgB,GAAGJ,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/B,YAAY,IAAI,EAAE;MAC7D,MAAMoC,cAAc,GAAGL,IAAI,CAACzB,UAAU,IAAI,CAAC,CAAC;MAE5CL,eAAe,CAACkC,gBAAgB,CAAC;MACjC5B,aAAa,CAAC6B,cAAc,IAAI;QAC9B5B,KAAK,EAAE2B,gBAAgB,CAACE,MAAM,IAAI,CAAC;QACnC5B,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE2B,IAAI,CAACC,IAAI,CAAC,CAACJ,gBAAgB,CAACE,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;QACzDzB,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFR,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZP,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEoC,GAAG,CAAC;MAClDnC,QAAQ,CAAC,sDAAsD,CAAC;MAChEJ,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIhC,IAAY,IAAK;IACzCF,aAAa,CAACmC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPjC;IACF,CAAC,CAAC,CAAC;IACH;IACAkC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,oBACE1D,OAAA;IAAK2D,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpC5D,OAAA;MAAK2D,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBAEnG5D,OAAA;QAAK2D,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3GhE,OAAA;QAAK2D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAClF5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5D,OAAA;YAAK2D,SAAS,EAAC,uHAAuH;YAAAC,QAAA,gBACpI5D,OAAA;cAAM2D,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7E9C,UAAU,CAACE,KAAK,EAAC,sBACpB;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENhE,OAAA;YAAI2D,SAAS,EAAC,8FAA8F;YAAAC,QAAA,GAAC,eAE3G,eAAA5D,OAAA;cAAM2D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAELhE,OAAA;YAAG2D,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAI7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJhE,OAAA;YAAK2D,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5F5D,OAAA;cAAK2D,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjF5D,OAAA;gBAAK2D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpEhE,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjF5D,OAAA;gBAAK2D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEhE,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjF5D,OAAA;gBAAK2D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnEhE,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,mEAAmE;MAAAC,QAAA,eAChF5D,OAAA,CAACF,WAAW;QACVmE,MAAM,EAAC,YAAY;QACnBC,MAAM,EAAC,aAAa;QACpBC,UAAU,EAAE,IAAK;QACjBC,SAAS,EAAE;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D5D,OAAA;QAAK2D,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAChG5D,OAAA;UAAK2D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5D,OAAA;YAAI2D,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEhE,OAAA;YACEqE,OAAO,EAAEA,CAAA,KAAM;cACb9D,cAAc,CAAC,EAAE,CAAC;cAClBE,gBAAgB,CAAC,EAAE,CAAC;cACpBE,kBAAkB,CAAC,EAAE,CAAC;cACtBQ,aAAa,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjC,IAAI,EAAE;cAAE,CAAC,CAAC,CAAC;YAC/C,CAAE;YACFsC,SAAS,EAAC,4EAA4E;YAAAC,QAAA,gBAEtF5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAACW,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eACjF5D,OAAA;gBAAMyE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL,CAAC,gCAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhE,OAAA;UAAK2D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnE5D,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAO6E,OAAO,EAAC,QAAQ;cAAClB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cAAK2D,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjD5D,OAAA;gBAAK2D,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF5D,OAAA;kBAAK2D,SAAS,EAAC,uBAAuB;kBAACW,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAZ,QAAA,eAC1F5D,OAAA;oBAAMyE,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA6C;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhE,OAAA;gBACE8E,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,QAAQ;gBACbC,EAAE,EAAC,QAAQ;gBACXC,KAAK,EAAE3E,WAAY;gBACnB4E,QAAQ,EAAGC,CAAC,IAAK;kBACf5E,cAAc,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBAC9B9D,aAAa,CAACmC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjC,IAAI,EAAE;kBAAE,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFsC,SAAS,EAAC,4GAA4G;gBACtH0B,WAAW,EAAC;cAA2B;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAO6E,OAAO,EAAC,OAAO;cAAClB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACEgF,EAAE,EAAC,OAAO;cACVD,IAAI,EAAC,OAAO;cACZE,KAAK,EAAEzE,aAAc;cACrB0E,QAAQ,EAAGC,CAAC,IAAK;gBACf1E,gBAAgB,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;gBAChC9D,aAAa,CAACmC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEjC,IAAI,EAAE;gBAAE,CAAC,CAAC,CAAC;cAC/C,CAAE;cACFsC,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK5D,OAAA;gBAAQiF,KAAK,EAAC,EAAE;gBAAArB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQiF,KAAK,EAAC,SAAS;gBAAArB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChE,OAAA;gBAAQiF,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQiF,KAAK,EAAC,UAAU;gBAAArB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQiF,KAAK,EAAC,eAAe;gBAAArB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAO6E,OAAO,EAAC,SAAS;cAAClB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACEgF,EAAE,EAAC,SAAS;cACZD,IAAI,EAAC,SAAS;cACdE,KAAK,EAAEvE,eAAgB;cACvBwE,QAAQ,EAAGC,CAAC,IAAK;gBACfxE,kBAAkB,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;gBAClC9D,aAAa,CAACmC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEjC,IAAI,EAAE;gBAAE,CAAC,CAAC,CAAC;cAC/C,CAAE;cACFsC,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK5D,OAAA;gBAAQiF,KAAK,EAAC,EAAE;gBAAArB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvChE,OAAA;gBAAQiF,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQiF,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQiF,KAAK,EAAC,UAAU;gBAAArB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQiF,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQiF,KAAK,EAAC,OAAO;gBAAArB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpChE,OAAA;gBAAQiF,KAAK,EAAC,SAAS;gBAAArB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChE,OAAA;gBAAQiF,KAAK,EAAC,eAAS;gBAAArB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChE,OAAA;gBAAQiF,KAAK,EAAC,kBAAe;gBAAArB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAO6E,OAAO,EAAC,QAAQ;cAAClB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACEgF,EAAE,EAAC,QAAQ;cACXD,IAAI,EAAC,QAAQ;cACbpB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK5D,OAAA;gBAAQiF,KAAK,EAAC,EAAE;gBAAArB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQiF,KAAK,EAAC,MAAM;gBAAArB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQiF,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvChE,OAAA;gBAAQiF,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3D5D,OAAA;QAAK2D,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9C5D,OAAA;UAAK2D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAE3B5D,OAAA;YAAK2D,SAAS,EAAC,4EAA4E;YAAAC,QAAA,gBACzF5D,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAI2D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFhE,OAAA;gBAAG2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EACzB,CAAC9C,OAAO,IAAI,CAACE,KAAK,IAAI,GAAGE,UAAU,CAACE,KAAK;cAAmB;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5D,OAAA;gBACE2D,SAAS,EAAC,mIAAmI;gBAAAC,QAAA,gBAE7I5D,OAAA;kBAAQiF,KAAK,EAAC,QAAQ;kBAAArB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7ChE,OAAA;kBAAQiF,KAAK,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhE,OAAA;kBAAQiF,KAAK,EAAC,WAAW;kBAAArB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELlD,OAAO,gBACNd,OAAA;YAAK2D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrD5D,OAAA,CAACR,IAAI;cAAC8F,IAAI,EAAC,OAAO;cAACC,GAAG,EAAC;YAA2B;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,GACJhD,KAAK,gBACPhB,OAAA,CAACP,KAAK;YACJ+F,OAAO,EAAC,QAAQ;YAChBC,WAAW,EAAEzE,KAAM;YACnB8D,IAAI,EAAC,OAAO;YACZY,QAAQ;YACR/B,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,gBAEFhE,OAAA,CAAAE,SAAA;YAAA0D,QAAA,gBACE5D,OAAA;cAAK2D,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClEhD,YAAY,CAAC+E,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnC7F,OAAA;gBAA0B2D,SAAS,EAAC,iBAAiB;gBAACmC,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAAjC,QAAA,eACjG5D,OAAA,CAACL,uBAAuB;kBACtBqF,EAAE,EAAEY,WAAW,CAACZ,EAAG;kBACnBgB,KAAK,EAAEJ,WAAW,CAACI,KAAM;kBACzBC,SAAS,EAAEL,WAAW,CAACK,SAAU;kBACjCC,QAAQ,EAAEN,WAAW,CAACM,QAAS;kBAC/BC,MAAM,EAAEP,WAAW,CAACO,MAAO;kBAC3BC,KAAK,EAAER,WAAW,CAACQ,KAAM;kBACzBC,OAAO,EAAET,WAAW,CAACS,OAAQ;kBAC7BhC,OAAO,EAAEA,CAACW,EAAE,EAAEsB,IAAI,KAAK/C,MAAM,CAAClD,QAAQ,CAACkG,IAAI,GAAGD,IAAI,GAAG,WAAWA,IAAI,EAAE,GAAG,iBAAiBtB,EAAE,EAAG;kBAC/Fa,KAAK,EAAEA;gBAAM;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC,GAXM4B,WAAW,CAACZ,EAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLpD,YAAY,CAACqC,MAAM,KAAK,CAAC,iBACxBjD,OAAA;cAAK2D,SAAS,EAAC,2EAA2E;cAAAC,QAAA,gBACxF5D,OAAA;gBAAK2D,SAAS,EAAC,iCAAiC;gBAACW,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAZ,QAAA,eACpG5D,OAAA;kBAAMyE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,GAAI;kBAACC,CAAC,EAAC;gBAAoF;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3J,CAAC,eACNhE,OAAA;gBAAI2D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFhE,OAAA;gBAAG2D,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhE,OAAA;gBACEqE,OAAO,EAAEA,CAAA,KAAM;kBACb9D,cAAc,CAAC,EAAE,CAAC;kBAClBE,gBAAgB,CAAC,EAAE,CAAC;kBACpBE,kBAAkB,CAAC,EAAE,CAAC;kBACtBQ,aAAa,CAACmC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjC,IAAI,EAAE;kBAAE,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFsC,SAAS,EAAC,+NAA+N;gBAAAC,QAAA,EAC1O;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA9C,UAAU,CAACE,KAAK,GAAG,CAAC,iBACnBpB,OAAA;cAAK2D,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC5D,OAAA,CAACT,UAAU;gBACTiH,OAAO,EAAEtF,UAAU,CAACG,IAAK;gBACzBD,KAAK,EAAEF,UAAU,CAACE,KAAM;gBACxBqF,QAAQ,EAAEvF,UAAU,CAACI,KAAM;gBAC3B4D,QAAQ,EAAE7B,gBAAiB;gBAC3BqD,eAAe,EAAE,KAAM;gBACvBC,eAAe;gBACfC,SAAS,EAAGxF,KAAK,IAAK,SAASA,KAAK,UAAW;gBAC/CuC,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAGNhE,OAAA,CAACJ,mBAAmB;UAClBiH,MAAM,EAAE;YACN/B,IAAI,EAAE,QAAiB;YACvBgC,WAAW,EAAEtG,aAAa,IAAIuG,SAAS;YACvCzF,KAAK,EAAE;UACT,CAAE;UACFqC,SAAS,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA,CAACH,kBAAkB;MACjBmH,eAAe,EAAC,aAAa;MAC7BF,WAAW,EAAEtG,aAAa,IAAIE;IAAgB;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEZ,CAAC;AAAC5D,EAAA,CA5XID,YAAsB;EAAA,QACTT,WAAW;AAAA;AAAAuH,EAAA,GADxB9G,YAAsB;AA8X5B,eAAeA,YAAY;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}