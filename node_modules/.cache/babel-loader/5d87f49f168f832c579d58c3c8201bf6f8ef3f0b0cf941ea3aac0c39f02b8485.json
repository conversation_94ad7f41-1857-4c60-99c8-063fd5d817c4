{"ast": null, "code": "/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\nmodule.exports = stackGet;", "map": {"version": 3, "names": ["stackGet", "key", "__data__", "get", "module", "exports"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lodash/_stackGet.js"], "sourcesContent": ["/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\nmodule.exports = stackGet;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAO,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACF,GAAG,CAAC;AAC/B;AAEAG,MAAM,CAACC,OAAO,GAAGL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}