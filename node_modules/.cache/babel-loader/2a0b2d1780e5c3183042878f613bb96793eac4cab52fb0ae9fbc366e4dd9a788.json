{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigationDropdown = ({\n  type,\n  label,\n  className = ''\n}) => {\n  _s();\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/pays',\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 8).map(country => ({\n            id: country.country.toLowerCase().replace(/\\s+/g, '-'),\n            label: country.country,\n            href: `/pays/${encodeURIComponent(country.country)}`,\n            count: country.count\n          })), ...(countries.length > 8 ? [{\n            id: 'view-all-countries',\n            label: translations.navigation.viewAll || 'View All',\n            href: '/pays'\n          }] : [])];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => {\n            // Function to map level names to correct routes\n            const getLevelRoute = levelName => {\n              const name = levelName.toLowerCase();\n              if (name.includes('licence') || name.includes('undergraduate') || name.includes('bachelor')) {\n                return '/licence';\n              } else if (name.includes('master')) {\n                return '/master';\n              } else if (name.includes('doctorat') || name.includes('doctorate') || name.includes('phd')) {\n                return '/doctorat';\n              }\n              // Fallback to the original logic for unknown levels\n              return `/${levelName.toLowerCase().replace(/\\s+/g, '-')}`;\n            };\n            return [{\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/bourses',\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            }, ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: getLevelRoute(level.name),\n              count: level.openCount\n            }))];\n          };\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities/type/${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/pays';\n      case 'scholarships':\n        return '/bourses';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n  const trigger = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      flex items-center rounded-md text-sm font-medium\n      transition-all duration-200 ease-in-out\n      ${className}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: getMainPageUrl(),\n      className: \"px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5 transition-colors duration-200\",\n      onClick: e => e.stopPropagation() // Prevent dropdown from opening when clicking the link\n      ,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"transition-colors duration-200\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-1 py-2 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer\",\n      onClick: e => {\n        e.stopPropagation();\n        // This will be handled by the dropdown's click handler\n      },\n      children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 14,\n        className: \"transition-all duration-200 ease-out group-hover:rotate-180\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group relative\",\n    children: /*#__PURE__*/_jsxDEV(Dropdown, {\n      trigger: trigger,\n      items: items,\n      loading: loading,\n      onOpen: fetchData,\n      showOnHover: true,\n      closeOnClick: true,\n      placement: \"bottom-left\",\n      className: \"\",\n      dropdownClassName: \"border-t-2 border-primary\",\n      emptyMessage: `No ${type} available`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(NavigationDropdown, \"dSYAOEQJpmUEaAadZBeAZJtT6G0=\", false, function () {\n  return [useLanguage];\n});\n_c = NavigationDropdown;\nexport default NavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"NavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "Link", "ChevronDown", "Dropdown", "useLanguage", "jsxDEV", "_jsxDEV", "NavigationDropdown", "type", "label", "className", "_s", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "count", "reduce", "sum", "c", "slice", "map", "country", "toLowerCase", "replace", "encodeURIComponent", "viewAll", "levels", "getLevelRoute", "levelName", "name", "includes", "allScholarships", "l", "level", "slug", "openCount", "types", "allOpportunities", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "disabled", "getMainPageUrl", "trigger", "children", "to", "onClick", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onOpen", "showOnHover", "closeOnClick", "placement", "dropdownClassName", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx"], "sourcesContent": ["/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown, { DropdownItem } from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface NavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  className?: string;\n}\n\ninterface CountryData {\n  country: string;\n  count: number;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst NavigationDropdown: React.FC<NavigationDropdownProps> = ({\n  type,\n  label,\n  className = ''\n}) => {\n  const [items, setItems] = useState<DropdownItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/pays',\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 8).map(country => ({\n              id: country.country.toLowerCase().replace(/\\s+/g, '-'),\n              label: country.country,\n              href: `/pays/${encodeURIComponent(country.country)}`,\n              count: country.count\n            })),\n            ...(countries.length > 8 ? [{\n              id: 'view-all-countries',\n              label: translations.navigation.viewAll || 'View All',\n              href: '/pays'\n            }] : [])\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => {\n            // Function to map level names to correct routes\n            const getLevelRoute = (levelName: string): string => {\n              const name = levelName.toLowerCase();\n              if (name.includes('licence') || name.includes('undergraduate') || name.includes('bachelor')) {\n                return '/licence';\n              } else if (name.includes('master')) {\n                return '/master';\n              } else if (name.includes('doctorat') || name.includes('doctorate') || name.includes('phd')) {\n                return '/doctorat';\n              }\n              // Fallback to the original logic for unknown levels\n              return `/${levelName.toLowerCase().replace(/\\s+/g, '-')}`;\n            };\n\n            return [\n              {\n                id: 'all-scholarships',\n                label: translations.navigation.allScholarships || 'All Scholarships',\n                href: '/bourses',\n                count: levels.reduce((sum, l) => sum + l.count, 0)\n              },\n              ...levels.map(level => ({\n                id: level.slug,\n                label: level.name,\n                href: getLevelRoute(level.name),\n                count: level.openCount\n              }))\n            ];\n          };\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities/type/${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/pays';\n      case 'scholarships':\n        return '/bourses';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n\n  const trigger = (\n    <div className={`\n      flex items-center rounded-md text-sm font-medium\n      transition-all duration-200 ease-in-out\n      ${className}\n    `}>\n      {/* Main clickable link */}\n      <Link\n        to={getMainPageUrl()}\n        className=\"px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5 transition-colors duration-200\"\n        onClick={(e) => e.stopPropagation()} // Prevent dropdown from opening when clicking the link\n      >\n        <span className=\"transition-colors duration-200\">{label}</span>\n      </Link>\n\n      {/* Dropdown arrow - separate clickable area */}\n      <div\n        className=\"px-1 py-2 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer\"\n        onClick={(e) => {\n          e.stopPropagation();\n          // This will be handled by the dropdown's click handler\n        }}\n      >\n        <ChevronDown\n          size={14}\n          className=\"transition-all duration-200 ease-out group-hover:rotate-180\"\n        />\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"group relative\">\n      <Dropdown\n        trigger={trigger}\n        items={items}\n        loading={loading}\n        onOpen={fetchData}\n        showOnHover={true}\n        closeOnClick={true}\n        placement=\"bottom-left\"\n        className=\"\"\n        dropdownClassName=\"border-t-2 border-primary\"\n        emptyMessage={`No ${type} available`}\n      />\n    </div>\n  );\n};\n\nexport default NavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,QAAQ,MAAwB,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA2B5D,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,IAAI;EACJC,KAAK;EACLC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEgB;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAA8C,GAAGA,CAAA,KAAM,EAAE;MAE7D,QAAQZ,IAAI;QACV,KAAK,WAAW;UACdW,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAEL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGL,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvCV,EAAE,EAAEU,OAAO,CAACA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YACtDzB,KAAK,EAAEuB,OAAO,CAACA,OAAO;YACtBP,IAAI,EAAE,SAASU,kBAAkB,CAACH,OAAO,CAACA,OAAO,CAAC,EAAE;YACpDN,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,EACH,IAAIL,SAAS,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;YAC1BI,EAAE,EAAE,oBAAoB;YACxBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACa,OAAO,IAAI,UAAU;YACpDX,IAAI,EAAE;UACR,CAAC,CAAC,GAAG,EAAE,CAAC,CACT;UACD;QAEF,KAAK,cAAc;UACjBN,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIiB,MAAmB,IAAK;YACvC;YACA,MAAMC,aAAa,GAAIC,SAAiB,IAAa;cACnD,MAAMC,IAAI,GAAGD,SAAS,CAACN,WAAW,CAAC,CAAC;cACpC,IAAIO,IAAI,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBAC3F,OAAO,UAAU;cACnB,CAAC,MAAM,IAAID,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBAClC,OAAO,SAAS;cAClB,CAAC,MAAM,IAAID,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC1F,OAAO,WAAW;cACpB;cACA;cACA,OAAO,IAAIF,SAAS,CAACN,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YAC3D,CAAC;YAED,OAAO,CACL;cACEZ,EAAE,EAAE,kBAAkB;cACtBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACmB,eAAe,IAAI,kBAAkB;cACpEjB,IAAI,EAAE,UAAU;cAChBC,KAAK,EAAEW,MAAM,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEe,CAAC,KAAKf,GAAG,GAAGe,CAAC,CAACjB,KAAK,EAAE,CAAC;YACnD,CAAC,EACD,GAAGW,MAAM,CAACN,GAAG,CAACa,KAAK,KAAK;cACtBtB,EAAE,EAAEsB,KAAK,CAACC,IAAI;cACdpC,KAAK,EAAEmC,KAAK,CAACJ,IAAI;cACjBf,IAAI,EAAEa,aAAa,CAACM,KAAK,CAACJ,IAAI,CAAC;cAC/Bd,KAAK,EAAEkB,KAAK,CAACE;YACf,CAAC,CAAC,CAAC,CACJ;UACH,CAAC;UACD;QAEF,KAAK,eAAe;UAClB3B,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAI2B,KAA4B,IAAK,CAChD;YACEzB,EAAE,EAAE,mBAAmB;YACvBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACyB,gBAAgB,IAAI,mBAAmB;YACtEvB,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAEqB,KAAK,CAACpB,MAAM,CAAC,CAACC,GAAG,EAAEqB,CAAC,KAAKrB,GAAG,GAAGqB,CAAC,CAACvB,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGqB,KAAK,CAAChB,GAAG,CAACmB,MAAM,KAAK;YACtB5B,EAAE,EAAE4B,MAAM,CAACL,IAAI;YACfpC,KAAK,EAAEyC,MAAM,CAACV,IAAI,CAACW,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACV,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC;YACjEL,IAAI,EAAE,uBAAuBU,kBAAkB,CAACe,MAAM,CAACV,IAAI,CAAC,EAAE;YAC9Dd,KAAK,EAAEwB,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGvC,QAAQ,EAAE,CAAC;MACtG,IAAI,CAACmC,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElChD,QAAQ,CAACO,aAAa,CAAC2C,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBxD,IAAI,QAAQ,EAAEwD,KAAK,CAAC;MACpDnD,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXb,KAAK,EAAE,qBAAqB;QAC5ByD,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRnD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQ3D,IAAI;MACV,KAAK,WAAW;QACd,OAAO,OAAO;MAChB,KAAK,cAAc;QACjB,OAAO,UAAU;MACnB,KAAK,eAAe;QAClB,OAAO,gBAAgB;MACzB;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,MAAM4D,OAAO,gBACX9D,OAAA;IAAKI,SAAS,EAAE;AACpB;AACA;AACA,QAAQA,SAAS;AACjB,KAAM;IAAA2D,QAAA,gBAEA/D,OAAA,CAACL,IAAI;MACHqE,EAAE,EAAEH,cAAc,CAAC,CAAE;MACrBzD,SAAS,EAAC,8FAA8F;MACxG6D,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;MAAA;MAAAJ,QAAA,eAErC/D,OAAA;QAAMI,SAAS,EAAC,gCAAgC;QAAA2D,QAAA,EAAE5D;MAAK;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAGPvE,OAAA;MACEI,SAAS,EAAC,0FAA0F;MACpG6D,OAAO,EAAGC,CAAC,IAAK;QACdA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnB;MACF,CAAE;MAAAJ,QAAA,eAEF/D,OAAA,CAACJ,WAAW;QACV4E,IAAI,EAAE,EAAG;QACTpE,SAAS,EAAC;MAA6D;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEvE,OAAA;IAAKI,SAAS,EAAC,gBAAgB;IAAA2D,QAAA,eAC7B/D,OAAA,CAACH,QAAQ;MACPiE,OAAO,EAAEA,OAAQ;MACjBxD,KAAK,EAAEA,KAAM;MACbE,OAAO,EAAEA,OAAQ;MACjBiE,MAAM,EAAE9D,SAAU;MAClB+D,WAAW,EAAE,IAAK;MAClBC,YAAY,EAAE,IAAK;MACnBC,SAAS,EAAC,aAAa;MACvBxE,SAAS,EAAC,EAAE;MACZyE,iBAAiB,EAAC,2BAA2B;MAC7CC,YAAY,EAAE,MAAM5E,IAAI;IAAa;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClE,EAAA,CA/KIJ,kBAAqD;EAAA,QAOhCH,WAAW;AAAA;AAAAiF,EAAA,GAPhC9E,kBAAqD;AAiL3D,eAAeA,kBAAkB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}