{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { offset } from \"../../util\";\nimport Panel from \"./Panel\";\nvar Content = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    title = props.title,\n    style = props.style,\n    className = props.className,\n    visible = props.visible,\n    forceRender = props.forceRender,\n    destroyOnClose = props.destroyOnClose,\n    motionName = props.motionName,\n    ariaId = props.ariaId,\n    onVisibleChanged = props.onVisibleChanged,\n    mousePosition = props.mousePosition;\n  var dialogRef = useRef();\n\n  // ============================= Style ==============================\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    transformOrigin = _React$useState2[0],\n    setTransformOrigin = _React$useState2[1];\n  var contentStyle = {};\n  if (transformOrigin) {\n    contentStyle.transformOrigin = transformOrigin;\n  }\n  function onPrepare() {\n    var elementOffset = offset(dialogRef.current);\n    setTransformOrigin(mousePosition && (mousePosition.x || mousePosition.y) ? \"\".concat(mousePosition.x - elementOffset.left, \"px \").concat(mousePosition.y - elementOffset.top, \"px\") : '');\n  }\n\n  // ============================= Render =============================\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    onVisibleChanged: onVisibleChanged,\n    onAppearPrepare: onPrepare,\n    onEnterPrepare: onPrepare,\n    forceRender: forceRender,\n    motionName: motionName,\n    removeOnLeave: destroyOnClose,\n    ref: dialogRef\n  }, function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(Panel, _extends({}, props, {\n      ref: ref,\n      title: title,\n      ariaId: ariaId,\n      prefixCls: prefixCls,\n      holderRef: motionRef,\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), style), contentStyle),\n      className: classNames(className, motionClassName)\n    }));\n  });\n});\nContent.displayName = 'Content';\nexport default Content;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "React", "useRef", "classNames", "CSSMotion", "offset", "Panel", "Content", "forwardRef", "props", "ref", "prefixCls", "title", "style", "className", "visible", "forceRender", "destroyOnClose", "motionName", "ariaId", "onVisibleChanged", "mousePosition", "dialogRef", "_React$useState", "useState", "_React$useState2", "transform<PERSON><PERSON>in", "setTransformOrigin", "contentStyle", "onPrepare", "elementOffset", "current", "x", "y", "concat", "left", "top", "createElement", "onAppearPrepare", "onEnterPrepare", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle", "holder<PERSON><PERSON>", "displayName"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-dialog/es/Dialog/Content/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { offset } from \"../../util\";\nimport Panel from \"./Panel\";\nvar Content = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    title = props.title,\n    style = props.style,\n    className = props.className,\n    visible = props.visible,\n    forceRender = props.forceRender,\n    destroyOnClose = props.destroyOnClose,\n    motionName = props.motionName,\n    ariaId = props.ariaId,\n    onVisibleChanged = props.onVisibleChanged,\n    mousePosition = props.mousePosition;\n  var dialogRef = useRef();\n\n  // ============================= Style ==============================\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    transformOrigin = _React$useState2[0],\n    setTransformOrigin = _React$useState2[1];\n  var contentStyle = {};\n  if (transformOrigin) {\n    contentStyle.transformOrigin = transformOrigin;\n  }\n  function onPrepare() {\n    var elementOffset = offset(dialogRef.current);\n    setTransformOrigin(mousePosition && (mousePosition.x || mousePosition.y) ? \"\".concat(mousePosition.x - elementOffset.left, \"px \").concat(mousePosition.y - elementOffset.top, \"px\") : '');\n  }\n\n  // ============================= Render =============================\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    onVisibleChanged: onVisibleChanged,\n    onAppearPrepare: onPrepare,\n    onEnterPrepare: onPrepare,\n    forceRender: forceRender,\n    motionName: motionName,\n    removeOnLeave: destroyOnClose,\n    ref: dialogRef\n  }, function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(Panel, _extends({}, props, {\n      ref: ref,\n      title: title,\n      ariaId: ariaId,\n      prefixCls: prefixCls,\n      holderRef: motionRef,\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), style), contentStyle),\n      className: classNames(className, motionClassName)\n    }));\n  });\n});\nContent.displayName = 'Content';\nexport default Content;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,OAAO,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAChE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCC,UAAU,GAAGT,KAAK,CAACS,UAAU;IAC7BC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,gBAAgB,GAAGX,KAAK,CAACW,gBAAgB;IACzCC,aAAa,GAAGZ,KAAK,CAACY,aAAa;EACrC,IAAIC,SAAS,GAAGpB,MAAM,CAAC,CAAC;;EAExB;EACA,IAAIqB,eAAe,GAAGtB,KAAK,CAACuB,QAAQ,CAAC,CAAC;IACpCC,gBAAgB,GAAGzB,cAAc,CAACuB,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIF,eAAe,EAAE;IACnBE,YAAY,CAACF,eAAe,GAAGA,eAAe;EAChD;EACA,SAASG,SAASA,CAAA,EAAG;IACnB,IAAIC,aAAa,GAAGzB,MAAM,CAACiB,SAAS,CAACS,OAAO,CAAC;IAC7CJ,kBAAkB,CAACN,aAAa,KAAKA,aAAa,CAACW,CAAC,IAAIX,aAAa,CAACY,CAAC,CAAC,GAAG,EAAE,CAACC,MAAM,CAACb,aAAa,CAACW,CAAC,GAAGF,aAAa,CAACK,IAAI,EAAE,KAAK,CAAC,CAACD,MAAM,CAACb,aAAa,CAACY,CAAC,GAAGH,aAAa,CAACM,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;EAC3L;;EAEA;EACA,OAAO,aAAanC,KAAK,CAACoC,aAAa,CAACjC,SAAS,EAAE;IACjDW,OAAO,EAAEA,OAAO;IAChBK,gBAAgB,EAAEA,gBAAgB;IAClCkB,eAAe,EAAET,SAAS;IAC1BU,cAAc,EAAEV,SAAS;IACzBb,WAAW,EAAEA,WAAW;IACxBE,UAAU,EAAEA,UAAU;IACtBsB,aAAa,EAAEvB,cAAc;IAC7BP,GAAG,EAAEY;EACP,CAAC,EAAE,UAAUmB,IAAI,EAAEC,SAAS,EAAE;IAC5B,IAAIC,eAAe,GAAGF,IAAI,CAAC3B,SAAS;MAClC8B,WAAW,GAAGH,IAAI,CAAC5B,KAAK;IAC1B,OAAO,aAAaZ,KAAK,CAACoC,aAAa,CAAC/B,KAAK,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;MACjEC,GAAG,EAAEA,GAAG;MACRE,KAAK,EAAEA,KAAK;MACZO,MAAM,EAAEA,MAAM;MACdR,SAAS,EAAEA,SAAS;MACpBkC,SAAS,EAAEH,SAAS;MACpB7B,KAAK,EAAEd,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6C,WAAW,CAAC,EAAE/B,KAAK,CAAC,EAAEe,YAAY,CAAC;MACxFd,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAE6B,eAAe;IAClD,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFpC,OAAO,CAACuC,WAAW,GAAG,SAAS;AAC/B,eAAevC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}