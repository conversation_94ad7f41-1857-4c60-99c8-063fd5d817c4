{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx\";\nimport React from 'react';\nimport StandardLevelPage from '../components/StandardLevelPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MasterPage = () => {\n  const pageConfig = {\n    level: 'Master',\n    title: 'Bourses d\\'Études de Master | Financement pour Études Supérieures de 2ème Cycle',\n    description: 'Explorez des opportunités de bourses d\\'études pour programmes de Master. Financez vos études supérieures avec des bourses internationales prestigieuses.',\n    keywords: 'bourses master, financement master, bourses deuxième cycle, études supérieures',\n    heroTitle: 'Bourses d\\'Études de',\n    heroSubtitle: 'Accédez aux programmes de Master les plus prestigieux avec des bourses d\\'excellence qui transformeront votre carrière professionnelle.',\n    infoTitle: 'Pourquoi Poursuivre un Master avec une Bourse ?',\n    infoContent: 'Un Master représente un investissement stratégique dans votre avenir professionnel. Avec une bourse d\\'études, vous pouvez vous concentrer pleinement sur l\\'excellence académique et la recherche, sans les contraintes financières.',\n    benefits: ['Spécialisation dans votre domaine d\\'expertise', 'Accès à des laboratoires et équipements de pointe', 'Encadrement par des professeurs renommés', 'Opportunités de recherche et de publication', 'Réseau professionnel international étendu', 'Préparation optimale pour le doctorat ou l\\'industrie'],\n    apiEndpoint: '/api/scholarships/search'\n  };\n  return /*#__PURE__*/_jsxDEV(StandardLevelPage, {\n    config: pageConfig\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 10\n  }, this);\n};\n_c = MasterPage;\nexport default MasterPage;\nvar _c;\n$RefreshReg$(_c, \"MasterPage\");", "map": {"version": 3, "names": ["React", "StandardLevelPage", "jsxDEV", "_jsxDEV", "MasterPage", "pageConfig", "level", "title", "description", "keywords", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "infoTitle", "infoContent", "benefits", "apiEndpoint", "config", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx"], "sourcesContent": ["import React from 'react';\nimport StandardLevelPage from '../components/StandardLevelPage';\n\nconst MasterPage: React.FC = () => {\n  const pageConfig = {\n    level: 'Master',\n    title: 'Bourses d\\'Études de Master | Financement pour Études Supérieures de 2ème Cycle',\n    description: 'Explorez des opportunités de bourses d\\'études pour programmes de Master. Financez vos études supérieures avec des bourses internationales prestigieuses.',\n    keywords: 'bourses master, financement master, bourses deuxième cycle, études supérieures',\n    heroTitle: 'Bourses d\\'Études de',\n    heroSubtitle: 'Accédez aux programmes de Master les plus prestigieux avec des bourses d\\'excellence qui transformeront votre carrière professionnelle.',\n    infoTitle: 'Pourquoi Poursuivre un Master avec une Bourse ?',\n    infoContent: 'Un Master représente un investissement stratégique dans votre avenir professionnel. Avec une bourse d\\'études, vous pouvez vous concentrer pleinement sur l\\'excellence académique et la recherche, sans les contraintes financières.',\n    benefits: [\n      'Spécialisation dans votre domaine d\\'expertise',\n      'Accès à des laboratoires et équipements de pointe',\n      'Encadrement par des professeurs renommés',\n      'Opportunités de recherche et de publication',\n      'Réseau professionnel international étendu',\n      'Préparation optimale pour le doctorat ou l\\'industrie'\n    ],\n    apiEndpoint: '/api/scholarships/search'\n  };\n\n  return <StandardLevelPage config={pageConfig} />;\n};\n\nexport default MasterPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,iFAAiF;IACxFC,WAAW,EAAE,2JAA2J;IACxKC,QAAQ,EAAE,gFAAgF;IAC1FC,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,yIAAyI;IACvJC,SAAS,EAAE,iDAAiD;IAC5DC,WAAW,EAAE,uOAAuO;IACpPC,QAAQ,EAAE,CACR,gDAAgD,EAChD,mDAAmD,EACnD,0CAA0C,EAC1C,6CAA6C,EAC7C,2CAA2C,EAC3C,uDAAuD,CACxD;IACDC,WAAW,EAAE;EACf,CAAC;EAED,oBAAOZ,OAAA,CAACF,iBAAiB;IAACe,MAAM,EAAEX;EAAW;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAClD,CAAC;AAACC,EAAA,GAtBIjB,UAAoB;AAwB1B,eAAeA,UAAU;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}