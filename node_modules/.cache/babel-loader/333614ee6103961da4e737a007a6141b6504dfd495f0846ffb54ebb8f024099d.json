{"ast": null, "code": "\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [hsbValue, setHsbValue] = useState(() => generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setHsbValue(value);\n    }\n  }, [value]);\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    if (!value) {\n      setHsbValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "generateColor", "getRoundNumber", "ColorSteppers", "ColorHsbInput", "_ref", "prefixCls", "value", "onChange", "colorHsbInputPrefixCls", "hsbValue", "setHsbValue", "handleHsbChange", "step", "type", "hsb", "toHsb", "genColor", "createElement", "className", "max", "min", "Number", "h", "formatter", "toString", "s", "b"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/antd/es/color-picker/components/ColorHsbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [hsbValue, setHsbValue] = useState(() => generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setHsbValue(value);\n    }\n  }, [value]);\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    if (!value) {\n      setHsbValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,EAAEC,cAAc,QAAQ,SAAS;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAGC,IAAI,IAAI;EAC5B,IAAI;IACFC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMI,sBAAsB,GAAG,GAAGH,SAAS,YAAY;EACvD,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,MAAMC,aAAa,CAACM,KAAK,IAAI,MAAM,CAAC,CAAC;EAC9E;EACAR,SAAS,CAAC,MAAM;IACd,IAAIQ,KAAK,EAAE;MACTI,WAAW,CAACJ,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAMK,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtC,MAAMC,GAAG,GAAGL,QAAQ,CAACM,KAAK,CAAC,CAAC;IAC5BD,GAAG,CAACD,IAAI,CAAC,GAAGA,IAAI,KAAK,GAAG,GAAGD,IAAI,GAAG,CAACA,IAAI,IAAI,CAAC,IAAI,GAAG;IACnD,MAAMI,QAAQ,GAAGhB,aAAa,CAACc,GAAG,CAAC;IACnC,IAAI,CAACR,KAAK,EAAE;MACVI,WAAW,CAACM,QAAQ,CAAC;IACvB;IACAT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACS,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAanB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEV;EACb,CAAC,EAAE,aAAaX,KAAK,CAACoB,aAAa,CAACf,aAAa,EAAE;IACjDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNd,KAAK,EAAEe,MAAM,CAACZ,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACO,CAAC,CAAC;IACjCjB,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEV,sBAAsB;IACjCe,SAAS,EAAEX,IAAI,IAAIX,cAAc,CAACW,IAAI,IAAI,CAAC,CAAC,CAACY,QAAQ,CAAC,CAAC;IACvDjB,QAAQ,EAAEK,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAaf,KAAK,CAACoB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNd,KAAK,EAAEe,MAAM,CAACZ,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACU,CAAC,CAAC,GAAG,GAAG;IACvCpB,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEV,sBAAsB;IACjCe,SAAS,EAAEX,IAAI,IAAI,GAAGX,cAAc,CAACW,IAAI,IAAI,CAAC,CAAC,GAAG;IAClDL,QAAQ,EAAEK,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAaf,KAAK,CAACoB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNd,KAAK,EAAEe,MAAM,CAACZ,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACW,CAAC,CAAC,GAAG,GAAG;IACvCrB,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEV,sBAAsB;IACjCe,SAAS,EAAEX,IAAI,IAAI,GAAGX,cAAc,CAACW,IAAI,IAAI,CAAC,CAAC,GAAG;IAClDL,QAAQ,EAAEK,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeT,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}