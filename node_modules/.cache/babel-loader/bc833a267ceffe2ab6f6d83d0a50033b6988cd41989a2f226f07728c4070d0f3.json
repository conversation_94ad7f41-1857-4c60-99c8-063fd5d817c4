{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectDestructuringEmpty from \"@babel/runtime/helpers/esm/objectDestructuringEmpty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { TreeContext } from \"./contextTypes\";\nimport TreeNode from \"./TreeNode\";\nimport useUnmount from \"./useUnmount\";\nimport { getTreeNodeProps } from \"./utils/treeUtil\";\nvar MotionTreeNode = /*#__PURE__*/React.forwardRef(function (oriProps, ref) {\n  var className = oriProps.className,\n    style = oriProps.style,\n    motion = oriProps.motion,\n    motionNodes = oriProps.motionNodes,\n    motionType = oriProps.motionType,\n    onOriginMotionStart = oriProps.onMotionStart,\n    onOriginMotionEnd = oriProps.onMotionEnd,\n    active = oriProps.active,\n    treeNodeRequiredProps = oriProps.treeNodeRequiredProps,\n    props = _objectWithoutProperties(oriProps, _excluded);\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useContext = React.useContext(TreeContext),\n    prefixCls = _React$useContext.prefixCls;\n\n  // Calculate target visible here.\n  // And apply in effect to make `leave` motion work.\n  var targetVisible = motionNodes && motionType !== 'hide';\n  useLayoutEffect(function () {\n    if (motionNodes) {\n      if (targetVisible !== visible) {\n        setVisible(targetVisible);\n      }\n    }\n  }, [motionNodes]);\n  var triggerMotionStart = function triggerMotionStart() {\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n  };\n\n  // Should only trigger once\n  var triggerMotionEndRef = React.useRef(false);\n  var triggerMotionEnd = function triggerMotionEnd() {\n    if (motionNodes && !triggerMotionEndRef.current) {\n      triggerMotionEndRef.current = true;\n      onOriginMotionEnd();\n    }\n  };\n\n  // Effect if unmount\n  useUnmount(triggerMotionStart, triggerMotionEnd);\n\n  // Motion end event\n  var onVisibleChanged = function onVisibleChanged(nextVisible) {\n    if (targetVisible === nextVisible) {\n      triggerMotionEnd();\n    }\n  };\n  if (motionNodes) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onVisibleChanged: onVisibleChanged\n    }), function (_ref, motionRef) {\n      var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: motionRef,\n        className: classNames(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = Object.assign({}, (_objectDestructuringEmpty(treeNode.data), treeNode.data)),\n          title = treeNode.title,\n          key = treeNode.key,\n          isStart = treeNode.isStart,\n          isEnd = treeNode.isEnd;\n        delete restProps.children;\n        var treeNodeProps = getTreeNodeProps(key, treeNodeRequiredProps);\n        return /*#__PURE__*/React.createElement(TreeNode, _extends({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n  return /*#__PURE__*/React.createElement(TreeNode, _extends({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  MotionTreeNode.displayName = 'MotionTreeNode';\n}\nexport default MotionTreeNode;", "map": {"version": 3, "names": ["_extends", "_objectDestructuringEmpty", "_slicedToArray", "_objectWithoutProperties", "_excluded", "classNames", "CSSMotion", "useLayoutEffect", "React", "TreeContext", "TreeNode", "useUnmount", "getTreeNodeProps", "MotionTreeNode", "forwardRef", "oriProps", "ref", "className", "style", "motion", "motionNodes", "motionType", "onOriginMotionStart", "onMotionStart", "onOriginMotionEnd", "onMotionEnd", "active", "treeNodeRequiredProps", "props", "_React$useState", "useState", "_React$useState2", "visible", "setVisible", "_React$useContext", "useContext", "prefixCls", "targetVisible", "triggerMotionStart", "triggerMotionEndRef", "useRef", "triggerMotionEnd", "current", "onVisibleChanged", "nextVisible", "createElement", "motionAppear", "_ref", "motionRef", "motionClassName", "motionStyle", "concat", "map", "treeNode", "restProps", "Object", "assign", "data", "title", "key", "isStart", "isEnd", "children", "treeNodeProps", "domRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-tree/es/MotionTreeNode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectDestructuringEmpty from \"@babel/runtime/helpers/esm/objectDestructuringEmpty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { TreeContext } from \"./contextTypes\";\nimport TreeNode from \"./TreeNode\";\nimport useUnmount from \"./useUnmount\";\nimport { getTreeNodeProps } from \"./utils/treeUtil\";\nvar MotionTreeNode = /*#__PURE__*/React.forwardRef(function (oriProps, ref) {\n  var className = oriProps.className,\n    style = oriProps.style,\n    motion = oriProps.motion,\n    motionNodes = oriProps.motionNodes,\n    motionType = oriProps.motionType,\n    onOriginMotionStart = oriProps.onMotionStart,\n    onOriginMotionEnd = oriProps.onMotionEnd,\n    active = oriProps.active,\n    treeNodeRequiredProps = oriProps.treeNodeRequiredProps,\n    props = _objectWithoutProperties(oriProps, _excluded);\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useContext = React.useContext(TreeContext),\n    prefixCls = _React$useContext.prefixCls;\n\n  // Calculate target visible here.\n  // And apply in effect to make `leave` motion work.\n  var targetVisible = motionNodes && motionType !== 'hide';\n  useLayoutEffect(function () {\n    if (motionNodes) {\n      if (targetVisible !== visible) {\n        setVisible(targetVisible);\n      }\n    }\n  }, [motionNodes]);\n  var triggerMotionStart = function triggerMotionStart() {\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n  };\n\n  // Should only trigger once\n  var triggerMotionEndRef = React.useRef(false);\n  var triggerMotionEnd = function triggerMotionEnd() {\n    if (motionNodes && !triggerMotionEndRef.current) {\n      triggerMotionEndRef.current = true;\n      onOriginMotionEnd();\n    }\n  };\n\n  // Effect if unmount\n  useUnmount(triggerMotionStart, triggerMotionEnd);\n\n  // Motion end event\n  var onVisibleChanged = function onVisibleChanged(nextVisible) {\n    if (targetVisible === nextVisible) {\n      triggerMotionEnd();\n    }\n  };\n  if (motionNodes) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onVisibleChanged: onVisibleChanged\n    }), function (_ref, motionRef) {\n      var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: motionRef,\n        className: classNames(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = Object.assign({}, (_objectDestructuringEmpty(treeNode.data), treeNode.data)),\n          title = treeNode.title,\n          key = treeNode.key,\n          isStart = treeNode.isStart,\n          isEnd = treeNode.isEnd;\n        delete restProps.children;\n        var treeNodeProps = getTreeNodeProps(key, treeNodeRequiredProps);\n        return /*#__PURE__*/React.createElement(TreeNode, _extends({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n  return /*#__PURE__*/React.createElement(TreeNode, _extends({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  MotionTreeNode.displayName = 'MotionTreeNode';\n}\nexport default MotionTreeNode;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,uBAAuB,CAAC;AAChJ,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,IAAIC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,UAAUC,QAAQ,EAAEC,GAAG,EAAE;EAC1E,IAAIC,SAAS,GAAGF,QAAQ,CAACE,SAAS;IAChCC,KAAK,GAAGH,QAAQ,CAACG,KAAK;IACtBC,MAAM,GAAGJ,QAAQ,CAACI,MAAM;IACxBC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IAClCC,UAAU,GAAGN,QAAQ,CAACM,UAAU;IAChCC,mBAAmB,GAAGP,QAAQ,CAACQ,aAAa;IAC5CC,iBAAiB,GAAGT,QAAQ,CAACU,WAAW;IACxCC,MAAM,GAAGX,QAAQ,CAACW,MAAM;IACxBC,qBAAqB,GAAGZ,QAAQ,CAACY,qBAAqB;IACtDC,KAAK,GAAGzB,wBAAwB,CAACY,QAAQ,EAAEX,SAAS,CAAC;EACvD,IAAIyB,eAAe,GAAGrB,KAAK,CAACsB,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAG7B,cAAc,CAAC2B,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,iBAAiB,GAAG1B,KAAK,CAAC2B,UAAU,CAAC1B,WAAW,CAAC;IACnD2B,SAAS,GAAGF,iBAAiB,CAACE,SAAS;;EAEzC;EACA;EACA,IAAIC,aAAa,GAAGjB,WAAW,IAAIC,UAAU,KAAK,MAAM;EACxDd,eAAe,CAAC,YAAY;IAC1B,IAAIa,WAAW,EAAE;MACf,IAAIiB,aAAa,KAAKL,OAAO,EAAE;QAC7BC,UAAU,CAACI,aAAa,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACjB,WAAW,CAAC,CAAC;EACjB,IAAIkB,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIlB,WAAW,EAAE;MACfE,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC;;EAED;EACA,IAAIiB,mBAAmB,GAAG/B,KAAK,CAACgC,MAAM,CAAC,KAAK,CAAC;EAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIrB,WAAW,IAAI,CAACmB,mBAAmB,CAACG,OAAO,EAAE;MAC/CH,mBAAmB,CAACG,OAAO,GAAG,IAAI;MAClClB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACAb,UAAU,CAAC2B,kBAAkB,EAAEG,gBAAgB,CAAC;;EAEhD;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,WAAW,EAAE;IAC5D,IAAIP,aAAa,KAAKO,WAAW,EAAE;MACjCH,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;EACD,IAAIrB,WAAW,EAAE;IACf,OAAO,aAAaZ,KAAK,CAACqC,aAAa,CAACvC,SAAS,EAAEN,QAAQ,CAAC;MAC1DgB,GAAG,EAAEA,GAAG;MACRgB,OAAO,EAAEA;IACX,CAAC,EAAEb,MAAM,EAAE;MACT2B,YAAY,EAAEzB,UAAU,KAAK,MAAM;MACnCsB,gBAAgB,EAAEA;IACpB,CAAC,CAAC,EAAE,UAAUI,IAAI,EAAEC,SAAS,EAAE;MAC7B,IAAIC,eAAe,GAAGF,IAAI,CAAC9B,SAAS;QAClCiC,WAAW,GAAGH,IAAI,CAAC7B,KAAK;MAC1B,OAAO,aAAaV,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAE;QAC7C7B,GAAG,EAAEgC,SAAS;QACd/B,SAAS,EAAEZ,UAAU,CAAC,EAAE,CAAC8C,MAAM,CAACf,SAAS,EAAE,kBAAkB,CAAC,EAAEa,eAAe,CAAC;QAChF/B,KAAK,EAAEgC;MACT,CAAC,EAAE9B,WAAW,CAACgC,GAAG,CAAC,UAAUC,QAAQ,EAAE;QACrC,IAAIC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,GAAGvD,yBAAyB,CAACoD,QAAQ,CAACI,IAAI,CAAC,EAAEJ,QAAQ,CAACI,IAAI,CAAC,CAAC;UAC1FC,KAAK,GAAGL,QAAQ,CAACK,KAAK;UACtBC,GAAG,GAAGN,QAAQ,CAACM,GAAG;UAClBC,OAAO,GAAGP,QAAQ,CAACO,OAAO;UAC1BC,KAAK,GAAGR,QAAQ,CAACQ,KAAK;QACxB,OAAOP,SAAS,CAACQ,QAAQ;QACzB,IAAIC,aAAa,GAAGnD,gBAAgB,CAAC+C,GAAG,EAAEhC,qBAAqB,CAAC;QAChE,OAAO,aAAanB,KAAK,CAACqC,aAAa,CAACnC,QAAQ,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEsD,SAAS,EAAES,aAAa,EAAE;UACvFL,KAAK,EAAEA,KAAK;UACZhC,MAAM,EAAEA,MAAM;UACd+B,IAAI,EAAEJ,QAAQ,CAACI,IAAI;UACnBE,GAAG,EAAEA,GAAG;UACRC,OAAO,EAAEA,OAAO;UAChBC,KAAK,EAAEA;QACT,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EACA,OAAO,aAAarD,KAAK,CAACqC,aAAa,CAACnC,QAAQ,EAAEV,QAAQ,CAAC;IACzDgE,MAAM,EAAEhD,GAAG;IACXC,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA;EACT,CAAC,EAAEU,KAAK,EAAE;IACRF,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIuC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCtD,cAAc,CAACuD,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAevD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}