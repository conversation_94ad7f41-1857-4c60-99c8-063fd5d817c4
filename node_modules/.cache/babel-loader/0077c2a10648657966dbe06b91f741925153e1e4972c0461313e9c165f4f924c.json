{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx\";\nimport React from 'react';\nimport StandardLevelPage from '../components/StandardLevelPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UndergraduatePage = () => {\n  const pageConfig = {\n    level: 'Licence',\n    title: 'Bourses d\\'Études de Licence | Opportunités de Financement pour Étudiants de Premier Cycle',\n    description: 'Découvrez des centaines de bourses d\\'études pour étudiants de licence. Financez vos études de premier cycle avec des opportunités de bourses internationales et nationales.',\n    keywords: 'bourses licence, financement études, bourses premier cycle, aide financière étudiants',\n    heroTitle: 'Bourses d\\'Études de',\n    heroSubtitle: 'Découvrez les meilleures opportunités de financement pour vos études de premier cycle et transformez votre avenir académique.',\n    infoTitle: 'Pourquoi Choisir une Bourse de Licence ?',\n    infoContent: 'Les études de licence représentent la première étape cruciale de votre parcours académique supérieur. Avec les coûts d\\'éducation en constante augmentation, obtenir une bourse d\\'études peut transformer votre avenir académique et professionnel.',\n    benefits: ['Accès à des universités prestigieuses sans le fardeau financier', 'Concentration totale sur vos études sans stress financier', 'Développement d\\'un réseau international de contacts', 'Expérience interculturelle enrichissante', 'Amélioration significative de votre CV pour votre future carrière', 'Opportunités de stages et d\\'emploi exclusives'],\n    apiEndpoint: '/api/scholarships/search'\n  };\n  return /*#__PURE__*/_jsxDEV(StandardLevelPage, {\n    config: pageConfig\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 10\n  }, this);\n};\n_c = UndergraduatePage;\nexport default UndergraduatePage;\nvar _c;\n$RefreshReg$(_c, \"UndergraduatePage\");", "map": {"version": 3, "names": ["React", "StandardLevelPage", "jsxDEV", "_jsxDEV", "UndergraduatePage", "pageConfig", "level", "title", "description", "keywords", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "infoTitle", "infoContent", "benefits", "apiEndpoint", "config", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx"], "sourcesContent": ["import React from 'react';\nimport StandardLevelPage from '../components/StandardLevelPage';\n\nconst UndergraduatePage: React.FC = () => {\n  const pageConfig = {\n    level: 'Licence',\n    title: 'Bourses d\\'Études de Licence | Opportunités de Financement pour Étudiants de Premier Cycle',\n    description: 'Découvrez des centaines de bourses d\\'études pour étudiants de licence. Financez vos études de premier cycle avec des opportunités de bourses internationales et nationales.',\n    keywords: 'bourses licence, financement études, bourses premier cycle, aide financière étudiants',\n    heroTitle: 'Bourses d\\'Études de',\n    heroSubtitle: 'Découvrez les meilleures opportunités de financement pour vos études de premier cycle et transformez votre avenir académique.',\n    infoTitle: 'Pourquoi Choisir une Bourse de Licence ?',\n    infoContent: 'Les études de licence représentent la première étape cruciale de votre parcours académique supérieur. Avec les coûts d\\'éducation en constante augmentation, obtenir une bourse d\\'études peut transformer votre avenir académique et professionnel.',\n    benefits: [\n      'Accès à des universités prestigieuses sans le fardeau financier',\n      'Concentration totale sur vos études sans stress financier',\n      'Développement d\\'un réseau international de contacts',\n      'Expérience interculturelle enrichissante',\n      'Amélioration significative de votre CV pour votre future carrière',\n      'Opportunités de stages et d\\'emploi exclusives'\n    ],\n    apiEndpoint: '/api/scholarships/search'\n  };\n\n  return <StandardLevelPage config={pageConfig} />;\n};\n\nexport default UndergraduatePage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EACxC,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,4FAA4F;IACnGC,WAAW,EAAE,8KAA8K;IAC3LC,QAAQ,EAAE,uFAAuF;IACjGC,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,+HAA+H;IAC7IC,SAAS,EAAE,0CAA0C;IACrDC,WAAW,EAAE,sPAAsP;IACnQC,QAAQ,EAAE,CACR,iEAAiE,EACjE,2DAA2D,EAC3D,sDAAsD,EACtD,0CAA0C,EAC1C,mEAAmE,EACnE,gDAAgD,CACjD;IACDC,WAAW,EAAE;EACf,CAAC;EAED,oBAAOZ,OAAA,CAACF,iBAAiB;IAACe,MAAM,EAAEX;EAAW;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAClD,CAAC;AAACC,EAAA,GAtBIjB,iBAA2B;AAwBjC,eAAeA,iBAAiB;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}