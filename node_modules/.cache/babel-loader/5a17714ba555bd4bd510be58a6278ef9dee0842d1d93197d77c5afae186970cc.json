{"ast": null, "code": "var isObject = require('./isObject');\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = function () {\n  function object() {}\n  return function (proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object();\n    object.prototype = undefined;\n    return result;\n  };\n}();\nmodule.exports = baseCreate;", "map": {"version": 3, "names": ["isObject", "require", "objectCreate", "Object", "create", "baseCreate", "object", "proto", "prototype", "result", "undefined", "module", "exports"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lodash/_baseCreate.js"], "sourcesContent": ["var isObject = require('./isObject');\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\nmodule.exports = baseCreate;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIC,YAAY,GAAGC,MAAM,CAACC,MAAM;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAI,YAAW;EAC3B,SAASC,MAAMA,CAAA,EAAG,CAAC;EACnB,OAAO,UAASC,KAAK,EAAE;IACrB,IAAI,CAACP,QAAQ,CAACO,KAAK,CAAC,EAAE;MACpB,OAAO,CAAC,CAAC;IACX;IACA,IAAIL,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACK,KAAK,CAAC;IAC5B;IACAD,MAAM,CAACE,SAAS,GAAGD,KAAK;IACxB,IAAIE,MAAM,GAAG,IAAIH,MAAM,CAAD,CAAC;IACvBA,MAAM,CAACE,SAAS,GAAGE,SAAS;IAC5B,OAAOD,MAAM;EACf,CAAC;AACH,CAAC,CAAC,CAAE;AAEJE,MAAM,CAACC,OAAO,GAAGP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}