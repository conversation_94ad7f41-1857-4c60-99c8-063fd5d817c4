{"ast": null, "code": "\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport InputNumber from '../../input-number';\nconst ColorSteppers = _ref => {\n  let {\n    prefixCls,\n    min = 0,\n    max = 100,\n    value,\n    onChange,\n    className,\n    formatter\n  } = _ref;\n  const colorSteppersPrefixCls = `${prefixCls}-steppers`;\n  const [stepValue, setStepValue] = useState(value);\n  // Update step value\n  useEffect(() => {\n    if (!Number.isNaN(value)) {\n      setStepValue(value);\n    }\n  }, [value]);\n  return /*#__PURE__*/React.createElement(InputNumber, {\n    className: classNames(colorSteppersPrefixCls, className),\n    min: min,\n    max: max,\n    value: stepValue,\n    formatter: formatter,\n    size: \"small\",\n    onChange: step => {\n      if (!value) {\n        setStepValue(step || 0);\n      }\n      onChange === null || onChange === void 0 ? void 0 : onChange(step);\n    }\n  });\n};\nexport default ColorSteppers;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "classNames", "InputNumber", "ColorSteppers", "_ref", "prefixCls", "min", "max", "value", "onChange", "className", "formatter", "colorSteppersPrefixCls", "<PERSON><PERSON><PERSON><PERSON>", "setStepValue", "Number", "isNaN", "createElement", "size", "step"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/antd/es/color-picker/components/ColorSteppers.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport InputNumber from '../../input-number';\nconst ColorSteppers = _ref => {\n  let {\n    prefixCls,\n    min = 0,\n    max = 100,\n    value,\n    onChange,\n    className,\n    formatter\n  } = _ref;\n  const colorSteppersPrefixCls = `${prefixCls}-steppers`;\n  const [stepValue, setStepValue] = useState(value);\n  // Update step value\n  useEffect(() => {\n    if (!Number.isNaN(value)) {\n      setStepValue(value);\n    }\n  }, [value]);\n  return /*#__PURE__*/React.createElement(InputNumber, {\n    className: classNames(colorSteppersPrefixCls, className),\n    min: min,\n    max: max,\n    value: stepValue,\n    formatter: formatter,\n    size: \"small\",\n    onChange: step => {\n      if (!value) {\n        setStepValue(step || 0);\n      }\n      onChange === null || onChange === void 0 ? void 0 : onChange(step);\n    }\n  });\n};\nexport default ColorSteppers;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,MAAMC,aAAa,GAAGC,IAAI,IAAI;EAC5B,IAAI;IACFC,SAAS;IACTC,GAAG,GAAG,CAAC;IACPC,GAAG,GAAG,GAAG;IACTC,KAAK;IACLC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGP,IAAI;EACR,MAAMQ,sBAAsB,GAAG,GAAGP,SAAS,WAAW;EACtD,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAACQ,KAAK,CAAC;EACjD;EACAT,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,MAAM,CAACC,KAAK,CAACR,KAAK,CAAC,EAAE;MACxBM,YAAY,CAACN,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAO,aAAaV,KAAK,CAACmB,aAAa,CAACf,WAAW,EAAE;IACnDQ,SAAS,EAAET,UAAU,CAACW,sBAAsB,EAAEF,SAAS,CAAC;IACxDJ,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAEK,SAAS;IAChBF,SAAS,EAAEA,SAAS;IACpBO,IAAI,EAAE,OAAO;IACbT,QAAQ,EAAEU,IAAI,IAAI;MAChB,IAAI,CAACX,KAAK,EAAE;QACVM,YAAY,CAACK,IAAI,IAAI,CAAC,CAAC;MACzB;MACAV,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,IAAI,CAAC;IACpE;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAehB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}