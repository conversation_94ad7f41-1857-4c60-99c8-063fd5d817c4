{"ast": null, "code": "import hasClass from './hasClass';\n/**\n * Adds a CSS class to a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\nexport default function addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!hasClass(element, className)) if (typeof element.className === 'string') element.className = element.className + \" \" + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + \" \" + className);\n}", "map": {"version": 3, "names": ["hasClass", "addClass", "element", "className", "classList", "add", "setAttribute", "baseVal"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/dom-helpers/esm/addClass.js"], "sourcesContent": ["import hasClass from './hasClass';\n/**\n * Adds a CSS class to a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\nexport default function addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!hasClass(element, className)) if (typeof element.className === 'string') element.className = element.className + \" \" + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + \" \" + className);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACnD,IAAID,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACE,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC,CAAC,KAAK,IAAI,CAACH,QAAQ,CAACE,OAAO,EAAEC,SAAS,CAAC,EAAE,IAAI,OAAOD,OAAO,CAACC,SAAS,KAAK,QAAQ,EAAED,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,GAAG,GAAG,GAAGA,SAAS,CAAC,KAAKD,OAAO,CAACI,YAAY,CAAC,OAAO,EAAE,CAACJ,OAAO,CAACC,SAAS,IAAID,OAAO,CAACC,SAAS,CAACI,OAAO,IAAI,EAAE,IAAI,GAAG,GAAGJ,SAAS,CAAC;AACjT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}