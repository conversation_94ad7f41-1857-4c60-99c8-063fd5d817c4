{"ast": null, "code": "import { hsl as colorHsl } from \"d3-color\";\nimport color, { hue } from \"./color.js\";\nfunction hsl(hue) {\n  return function (start, end) {\n    var h = hue((start = colorHsl(start)).h, (end = colorHsl(end)).h),\n      s = color(start.s, end.s),\n      l = color(start.l, end.l),\n      opacity = color(start.opacity, end.opacity);\n    return function (t) {\n      start.h = h(t);\n      start.s = s(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  };\n}\nexport default hsl(hue);\nexport var hslLong = hsl(color);", "map": {"version": 3, "names": ["hsl", "colorHsl", "color", "hue", "start", "end", "h", "s", "l", "opacity", "t", "hslLong"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-interpolate/src/hsl.js"], "sourcesContent": ["import {hsl as colorHsl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hsl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHsl(start)).h, (end = colorHsl(end)).h),\n        s = color(start.s, end.s),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.s = s(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hsl(hue);\nexport var hslLong = hsl(color);\n"], "mappings": "AAAA,SAAQA,GAAG,IAAIC,QAAQ,QAAO,UAAU;AACxC,OAAOC,KAAK,IAAGC,GAAG,QAAO,YAAY;AAErC,SAASH,GAAGA,CAACG,GAAG,EAAE;EAChB,OAAO,UAASC,KAAK,EAAEC,GAAG,EAAE;IAC1B,IAAIC,CAAC,GAAGH,GAAG,CAAC,CAACC,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAGJ,QAAQ,CAACI,GAAG,CAAC,EAAEC,CAAC,CAAC;MAC7DC,CAAC,GAAGL,KAAK,CAACE,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;MACzBC,CAAC,GAAGN,KAAK,CAACE,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;MACzBC,OAAO,GAAGP,KAAK,CAACE,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;IAC/C,OAAO,UAASC,CAAC,EAAE;MACjBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;MACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;MACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;MACdN,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;MAC1B,OAAON,KAAK,GAAG,EAAE;IACnB,CAAC;EACH,CAAC;AACH;AAEA,eAAeJ,GAAG,CAACG,GAAG,CAAC;AACvB,OAAO,IAAIQ,OAAO,GAAGX,GAAG,CAACE,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}