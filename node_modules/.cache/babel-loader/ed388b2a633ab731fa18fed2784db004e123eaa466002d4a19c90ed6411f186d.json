{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport AdPlacement from '../components/AdPlacement';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UndergraduatePage = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    page: 1,\n    limit: 12,\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Handle scholarship card click\n  const handleScholarshipClick = (id, slug) => {\n    if (slug) {\n      window.location.href = `/bourse/${slug}`;\n    } else {\n      window.location.href = `/scholarships/${id}`;\n    }\n  };\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const params = new URLSearchParams({\n        level: 'Licence',\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString()\n      });\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/scholarships/search?${params.toString()}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      if (data.success) {\n        setScholarships(data.data || []);\n        setPagination(prev => {\n          var _data$pagination, _data$pagination2, _data$pagination3, _data$pagination4;\n          return {\n            ...prev,\n            total: ((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.total) || 0,\n            totalPages: ((_data$pagination2 = data.pagination) === null || _data$pagination2 === void 0 ? void 0 : _data$pagination2.totalPages) || 0,\n            hasNextPage: ((_data$pagination3 = data.pagination) === null || _data$pagination3 === void 0 ? void 0 : _data$pagination3.hasNextPage) || false,\n            hasPreviousPage: ((_data$pagination4 = data.pagination) === null || _data$pagination4 === void 0 ? void 0 : _data$pagination4.hasPreviousPage) || false\n          };\n        });\n      } else {\n        throw new Error(data.message || 'Failed to fetch scholarships');\n      }\n    } catch (error) {\n      console.error('Error fetching undergraduate scholarships:', error);\n      setError('Erreur lors du chargement des bourses. Veuillez réessayer.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page]);\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const sidebarConfig = {\n    type: 'levels',\n    currentItem: 'Licence',\n    limit: 10\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Bourses d'\\xC9tudes de Licence | Opportunit\\xE9s de Financement pour \\xC9tudiants de Premier Cycle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"D\\xE9couvrez des centaines de bourses d'\\xE9tudes pour \\xE9tudiants de licence. Financez vos \\xE9tudes de premier cycle avec des opportunit\\xE9s de bourses internationales et nationales.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"keywords\",\n        content: \"bourses licence, financement \\xE9tudes, bourses premier cycle, aide financi\\xE8re \\xE9tudiants\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), pagination.total, \" bourses disponibles\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n              children: [\"Bourses d'\\xC9tudes de\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block text-yellow-300\",\n                children: \"Licence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed\",\n              children: \"D\\xE9couvrez des opportunit\\xE9s de financement exceptionnelles pour vos \\xE9tudes de premier cycle. Notre plateforme vous connecte avec des bourses d'\\xE9tudes nationales et internationales sp\\xE9cialement con\\xE7ues pour les \\xE9tudiants de licence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                  children: \"500+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-100\",\n                  children: \"Bourses Disponibles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                  children: \"50+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-100\",\n                  children: \"Pays Partenaires\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-100\",\n                  children: \"Taux de R\\xE9ussite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 hidden md:block\",\n        children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n          adSlot: \"3456789012\",\n          adSize: \"leaderboard\",\n          responsive: true,\n          fullWidth: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-2/3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                children: \"Pourquoi Choisir une Bourse de Licence ?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-blue max-w-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 leading-relaxed mb-4\",\n                  children: \"Les \\xE9tudes de licence repr\\xE9sentent la premi\\xE8re \\xE9tape cruciale de votre parcours acad\\xE9mique sup\\xE9rieur. Avec les co\\xFBts d'\\xE9ducation en constante augmentation, obtenir une bourse d'\\xE9tudes peut transformer votre avenir acad\\xE9mique et professionnel.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"Financement Complet\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Frais de scolarit\\xE9, logement et frais de subsistance couverts\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-blue-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 197,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"Excellence Acad\\xE9mique\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Acc\\xE8s aux meilleures universit\\xE9s mondiales\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: \"Bourses de Licence Disponibles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: !loading && !error && `${pagination.total} résultats`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center items-center py-16\",\n                children: /*#__PURE__*/_jsxDEV(Spin, {\n                  size: \"large\",\n                  tip: \"Chargement des bourses...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"Erreur\",\n                description: error,\n                type: \"error\",\n                showIcon: true,\n                className: \"mb-6 rounded-xl shadow-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-8 md:hidden\",\n                  children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n                    adSlot: \"4567890123\",\n                    adSize: \"rectangle\",\n                    responsive: true,\n                    fullWidth: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2\",\n                  children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-fade-in\",\n                    style: {\n                      animationDelay: `${index * 0.1}s`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                      id: scholarship.id,\n                      title: scholarship.title,\n                      thumbnail: scholarship.thumbnail,\n                      deadline: scholarship.deadline,\n                      isOpen: scholarship.isOpen,\n                      level: scholarship.level,\n                      country: scholarship.country,\n                      fundingSource: scholarship.fundingSource,\n                      onClick: handleScholarshipClick,\n                      index: index\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 27\n                    }, this)\n                  }, scholarship.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center mt-12\",\n                  children: /*#__PURE__*/_jsxDEV(Pagination, {\n                    current: pagination.page,\n                    total: pagination.total,\n                    pageSize: pagination.limit,\n                    onChange: handlePageChange,\n                    showSizeChanger: false,\n                    showQuickJumper: true,\n                    showTotal: total => `Total ${total} bourses`,\n                    className: \"shadow-sm rounded-xl p-2 bg-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProfessionalSidebar, {\n            config: sidebarConfig,\n            className: \"lg:w-1/3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageEndSuggestions, {\n        currentPageType: \"level\",\n        currentItem: \"Licence\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(UndergraduatePage, \"tY76+N1AuoLNPsF1IJd7vW0yoZc=\", false, function () {\n  return [useLanguage];\n});\n_c = UndergraduatePage;\nexport default UndergraduatePage;\nvar _c;\n$RefreshReg$(_c, \"UndergraduatePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "useLanguage", "EnhancedScholarshipCard", "ProfessionalSidebar", "PageEndSuggestions", "AdPlacement", "Pagination", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UndergraduatePage", "_s", "translations", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "total", "page", "limit", "totalPages", "hasNextPage", "hasPreviousPage", "handleScholarshipClick", "id", "slug", "window", "location", "href", "fetchScholarships", "params", "URLSearchParams", "level", "toString", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "Error", "data", "json", "success", "prev", "_data$pagination", "_data$pagination2", "_data$pagination3", "_data$pagination4", "message", "console", "handlePageChange", "scrollTo", "top", "behavior", "sidebarConfig", "type", "currentItem", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "adSlot", "adSize", "responsive", "fullWidth", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "size", "tip", "description", "showIcon", "map", "scholarship", "index", "style", "animationDelay", "title", "thumbnail", "deadline", "isOpen", "country", "fundingSource", "onClick", "current", "pageSize", "onChange", "showSizeChanger", "showQuickJumper", "showTotal", "config", "currentPageType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport AdPlacement from '../components/AdPlacement';\nimport { Pagination, Spin, Alert } from 'antd';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  country: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail: string;\n  fundingSource?: string;\n}\n\ninterface PaginationData {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nconst UndergraduatePage: React.FC = () => {\n  const { translations } = useLanguage();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pagination, setPagination] = useState<PaginationData>({\n    total: 0,\n    page: 1,\n    limit: 12,\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Handle scholarship card click\n  const handleScholarshipClick = (id: number, slug?: string) => {\n    if (slug) {\n      window.location.href = `/bourse/${slug}`;\n    } else {\n      window.location.href = `/scholarships/${id}`;\n    }\n  };\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const params = new URLSearchParams({\n        level: 'Licence',\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString()\n      });\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/scholarships/search?${params.toString()}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setScholarships(data.data || []);\n        setPagination(prev => ({\n          ...prev,\n          total: data.pagination?.total || 0,\n          totalPages: data.pagination?.totalPages || 0,\n          hasNextPage: data.pagination?.hasNextPage || false,\n          hasPreviousPage: data.pagination?.hasPreviousPage || false\n        }));\n      } else {\n        throw new Error(data.message || 'Failed to fetch scholarships');\n      }\n    } catch (error) {\n      console.error('Error fetching undergraduate scholarships:', error);\n      setError('Erreur lors du chargement des bourses. Veuillez réessayer.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page]);\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }));\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const sidebarConfig = {\n    type: 'levels' as const,\n    currentItem: 'Licence',\n    limit: 10\n  };\n\n  return (\n    <>\n      <Helmet>\n        <title>Bourses d'Études de Licence | Opportunités de Financement pour Étudiants de Premier Cycle</title>\n        <meta name=\"description\" content=\"Découvrez des centaines de bourses d'études pour étudiants de licence. Financez vos études de premier cycle avec des opportunités de bourses internationales et nationales.\" />\n        <meta name=\"keywords\" content=\"bourses licence, financement études, bourses premier cycle, aide financière étudiants\" />\n      </Helmet>\n\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n        {/* Hero Section */}\n        <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <div className=\"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n                {pagination.total} bourses disponibles\n              </div>\n              \n              <h1 className=\"text-4xl md:text-6xl font-bold mb-6 leading-tight\">\n                Bourses d'Études de\n                <span className=\"block text-yellow-300\">Licence</span>\n              </h1>\n              \n              <p className=\"text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed\">\n                Découvrez des opportunités de financement exceptionnelles pour vos études de premier cycle. \n                Notre plateforme vous connecte avec des bourses d'études nationales et internationales \n                spécialement conçues pour les étudiants de licence.\n              </p>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\">\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                  <div className=\"text-3xl font-bold text-yellow-300 mb-2\">500+</div>\n                  <div className=\"text-blue-100\">Bourses Disponibles</div>\n                </div>\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                  <div className=\"text-3xl font-bold text-yellow-300 mb-2\">50+</div>\n                  <div className=\"text-blue-100\">Pays Partenaires</div>\n                </div>\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                  <div className=\"text-3xl font-bold text-yellow-300 mb-2\">95%</div>\n                  <div className=\"text-blue-100\">Taux de Réussite</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Ad Placement - Top Banner */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 hidden md:block\">\n          <AdPlacement\n            adSlot=\"3456789012\"\n            adSize=\"leaderboard\"\n            responsive={true}\n            fullWidth={true}\n          />\n        </div>\n\n        {/* Content Section */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"flex flex-col lg:flex-row gap-8\">\n            {/* Main Content */}\n            <div className=\"lg:w-2/3\">\n              {/* Info Section */}\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 mb-8\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  Pourquoi Choisir une Bourse de Licence ?\n                </h2>\n                <div className=\"prose prose-blue max-w-none\">\n                  <p className=\"text-gray-600 leading-relaxed mb-4\">\n                    Les études de licence représentent la première étape cruciale de votre parcours académique supérieur. \n                    Avec les coûts d'éducation en constante augmentation, obtenir une bourse d'études peut transformer \n                    votre avenir académique et professionnel.\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\">\n                        <svg className=\"w-4 h-4 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">Financement Complet</h4>\n                        <p className=\"text-gray-600 text-sm\">Frais de scolarité, logement et frais de subsistance couverts</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1\">\n                        <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">Excellence Académique</h4>\n                        <p className=\"text-gray-600 text-sm\">Accès aux meilleures universités mondiales</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Scholarships Grid */}\n              <div className=\"mb-8\">\n                <div className=\"flex justify-between items-center mb-6\">\n                  <h2 className=\"text-2xl font-bold text-gray-900\">\n                    Bourses de Licence Disponibles\n                  </h2>\n                  <div className=\"text-sm text-gray-600\">\n                    {!loading && !error && `${pagination.total} résultats`}\n                  </div>\n                </div>\n\n                {loading ? (\n                  <div className=\"flex justify-center items-center py-16\">\n                    <Spin size=\"large\" tip=\"Chargement des bourses...\" />\n                  </div>\n                ) : error ? (\n                  <Alert\n                    message=\"Erreur\"\n                    description={error}\n                    type=\"error\"\n                    showIcon\n                    className=\"mb-6 rounded-xl shadow-md\"\n                  />\n                ) : (\n                  <>\n                    {/* Mobile Ad - Only visible on small screens */}\n                    <div className=\"mb-8 md:hidden\">\n                      <AdPlacement\n                        adSlot=\"4567890123\"\n                        adSize=\"rectangle\"\n                        responsive={true}\n                        fullWidth={true}\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2\">\n                      {scholarships.map((scholarship, index) => (\n                        <div key={scholarship.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                          <EnhancedScholarshipCard\n                            id={scholarship.id}\n                            title={scholarship.title}\n                            thumbnail={scholarship.thumbnail}\n                            deadline={scholarship.deadline}\n                            isOpen={scholarship.isOpen}\n                            level={scholarship.level}\n                            country={scholarship.country}\n                            fundingSource={scholarship.fundingSource}\n                            onClick={handleScholarshipClick}\n                            index={index}\n                          />\n                        </div>\n                      ))}\n                    </div>\n\n                    {/* Pagination */}\n                    {pagination.total > 0 && (\n                      <div className=\"flex justify-center mt-12\">\n                        <Pagination\n                          current={pagination.page}\n                          total={pagination.total}\n                          pageSize={pagination.limit}\n                          onChange={handlePageChange}\n                          showSizeChanger={false}\n                          showQuickJumper\n                          showTotal={(total) => `Total ${total} bourses`}\n                          className=\"shadow-sm rounded-xl p-2 bg-white\"\n                        />\n                      </div>\n                    )}\n                  </>\n                )}\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <ProfessionalSidebar \n              config={sidebarConfig}\n              className=\"lg:w-1/3\"\n            />\n          </div>\n        </div>\n\n        {/* Page End Suggestions */}\n        <PageEndSuggestions\n          currentPageType=\"level\"\n          currentItem=\"Licence\"\n        />\n      </div>\n    </>\n  );\n};\n\nexport default UndergraduatePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAuB/C,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;EACtC,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAiB;IAC3D0B,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,sBAAsB,GAAGA,CAACC,EAAU,EAAEC,IAAa,KAAK;IAC5D,IAAIA,IAAI,EAAE;MACRC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAWH,IAAI,EAAE;IAC1C,CAAC,MAAM;MACLC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBJ,EAAE,EAAE;IAC9C;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMgB,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,KAAK,EAAE,SAAS;QAChBd,IAAI,EAAEH,UAAU,CAACG,IAAI,CAACe,QAAQ,CAAC,CAAC;QAChCd,KAAK,EAAEJ,UAAU,CAACI,KAAK,CAACc,QAAQ,CAAC;MACnC,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,4BAA4BJ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAEtF,IAAI,CAACK,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBlC,eAAe,CAACgC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChC1B,aAAa,CAAC6B,IAAI;UAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAAA,OAAK;YACrB,GAAGJ,IAAI;YACP5B,KAAK,EAAE,EAAA6B,gBAAA,GAAAJ,IAAI,CAAC3B,UAAU,cAAA+B,gBAAA,uBAAfA,gBAAA,CAAiB7B,KAAK,KAAI,CAAC;YAClCG,UAAU,EAAE,EAAA2B,iBAAA,GAAAL,IAAI,CAAC3B,UAAU,cAAAgC,iBAAA,uBAAfA,iBAAA,CAAiB3B,UAAU,KAAI,CAAC;YAC5CC,WAAW,EAAE,EAAA2B,iBAAA,GAAAN,IAAI,CAAC3B,UAAU,cAAAiC,iBAAA,uBAAfA,iBAAA,CAAiB3B,WAAW,KAAI,KAAK;YAClDC,eAAe,EAAE,EAAA2B,iBAAA,GAAAP,IAAI,CAAC3B,UAAU,cAAAkC,iBAAA,uBAAfA,iBAAA,CAAiB3B,eAAe,KAAI;UACvD,CAAC;QAAA,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAImB,KAAK,CAACC,IAAI,CAACQ,OAAO,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdsC,OAAO,CAACtC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEC,QAAQ,CAAC,4DAA4D,CAAC;IACxE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACdqC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACd,UAAU,CAACG,IAAI,CAAC,CAAC;EAErB,MAAMkC,gBAAgB,GAAIlC,IAAY,IAAK;IACzCF,aAAa,CAAC6B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3B;IAAK,CAAC,CAAC,CAAC;IAC1CQ,MAAM,CAAC2B,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBC,IAAI,EAAE,QAAiB;IACvBC,WAAW,EAAE,SAAS;IACtBvC,KAAK,EAAE;EACT,CAAC;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAAsD,QAAA,gBACExD,OAAA,CAACV,MAAM;MAAAkE,QAAA,gBACLxD,OAAA;QAAAwD,QAAA,EAAO;MAAyF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxG5D,OAAA;QAAM6D,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA6K;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjN5D,OAAA;QAAM6D,IAAI,EAAC,UAAU;QAACC,OAAO,EAAC;MAAuF;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClH,CAAC,eAET5D,OAAA;MAAK+D,SAAS,EAAC,2DAA2D;MAAAP,QAAA,gBAExExD,OAAA;QAAK+D,SAAS,EAAC,+DAA+D;QAAAP,QAAA,eAC5ExD,OAAA;UAAK+D,SAAS,EAAC,wCAAwC;UAAAP,QAAA,eACrDxD,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAP,QAAA,gBAC1BxD,OAAA;cAAK+D,SAAS,EAAC,uGAAuG;cAAAP,QAAA,gBACpHxD,OAAA;gBAAM+D,SAAS,EAAC;cAAsD;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC7EhD,UAAU,CAACE,KAAK,EAAC,sBACpB;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEN5D,OAAA;cAAI+D,SAAS,EAAC,mDAAmD;cAAAP,QAAA,GAAC,wBAEhE,eAAAxD,OAAA;gBAAM+D,SAAS,EAAC,uBAAuB;gBAAAP,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAEL5D,OAAA;cAAG+D,SAAS,EAAC,0EAA0E;cAAAP,QAAA,EAAC;YAIxF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ5D,OAAA;cAAK+D,SAAS,EAAC,+DAA+D;cAAAP,QAAA,gBAC5ExD,OAAA;gBAAK+D,SAAS,EAAC,oEAAoE;gBAAAP,QAAA,gBACjFxD,OAAA;kBAAK+D,SAAS,EAAC,yCAAyC;kBAAAP,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnE5D,OAAA;kBAAK+D,SAAS,EAAC,eAAe;kBAAAP,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACN5D,OAAA;gBAAK+D,SAAS,EAAC,oEAAoE;gBAAAP,QAAA,gBACjFxD,OAAA;kBAAK+D,SAAS,EAAC,yCAAyC;kBAAAP,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClE5D,OAAA;kBAAK+D,SAAS,EAAC,eAAe;kBAAAP,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN5D,OAAA;gBAAK+D,SAAS,EAAC,oEAAoE;gBAAAP,QAAA,gBACjFxD,OAAA;kBAAK+D,SAAS,EAAC,yCAAyC;kBAAAP,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClE5D,OAAA;kBAAK+D,SAAS,EAAC,eAAe;kBAAAP,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5D,OAAA;QAAK+D,SAAS,EAAC,6DAA6D;QAAAP,QAAA,eAC1ExD,OAAA,CAACL,WAAW;UACVqE,MAAM,EAAC,YAAY;UACnBC,MAAM,EAAC,aAAa;UACpBC,UAAU,EAAE,IAAK;UACjBC,SAAS,EAAE;QAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5D,OAAA;QAAK+D,SAAS,EAAC,8CAA8C;QAAAP,QAAA,eAC3DxD,OAAA;UAAK+D,SAAS,EAAC,iCAAiC;UAAAP,QAAA,gBAE9CxD,OAAA;YAAK+D,SAAS,EAAC,UAAU;YAAAP,QAAA,gBAEvBxD,OAAA;cAAK+D,SAAS,EAAC,yCAAyC;cAAAP,QAAA,gBACtDxD,OAAA;gBAAI+D,SAAS,EAAC,uCAAuC;gBAAAP,QAAA,EAAC;cAEtD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5D,OAAA;gBAAK+D,SAAS,EAAC,6BAA6B;gBAAAP,QAAA,gBAC1CxD,OAAA;kBAAG+D,SAAS,EAAC,oCAAoC;kBAAAP,QAAA,EAAC;gBAIlD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ5D,OAAA;kBAAK+D,SAAS,EAAC,4CAA4C;kBAAAP,QAAA,gBACzDxD,OAAA;oBAAK+D,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,gBACzCxD,OAAA;sBAAK+D,SAAS,EAAC,uFAAuF;sBAAAP,QAAA,eACpGxD,OAAA;wBAAK+D,SAAS,EAAC,wBAAwB;wBAACK,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAd,QAAA,eAC3FxD,OAAA;0BAAMuE,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAAgB;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5D,OAAA;sBAAAwD,QAAA,gBACExD,OAAA;wBAAI+D,SAAS,EAAC,6BAA6B;wBAAAP,QAAA,EAAC;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpE5D,OAAA;wBAAG+D,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAC;sBAA6D;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5D,OAAA;oBAAK+D,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,gBACzCxD,OAAA;sBAAK+D,SAAS,EAAC,sFAAsF;sBAAAP,QAAA,eACnGxD,OAAA;wBAAK+D,SAAS,EAAC,uBAAuB;wBAACK,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAd,QAAA,eAC1FxD,OAAA;0BAAMuE,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAAoP;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5D,OAAA;sBAAAwD,QAAA,gBACExD,OAAA;wBAAI+D,SAAS,EAAC,6BAA6B;wBAAAP,QAAA,EAAC;sBAAqB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtE5D,OAAA;wBAAG+D,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAC;sBAA0C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5D,OAAA;cAAK+D,SAAS,EAAC,MAAM;cAAAP,QAAA,gBACnBxD,OAAA;gBAAK+D,SAAS,EAAC,wCAAwC;gBAAAP,QAAA,gBACrDxD,OAAA;kBAAI+D,SAAS,EAAC,kCAAkC;kBAAAP,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5D,OAAA;kBAAK+D,SAAS,EAAC,uBAAuB;kBAAAP,QAAA,EACnC,CAAChD,OAAO,IAAI,CAACE,KAAK,IAAI,GAAGE,UAAU,CAACE,KAAK;gBAAY;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELpD,OAAO,gBACNR,OAAA;gBAAK+D,SAAS,EAAC,wCAAwC;gBAAAP,QAAA,eACrDxD,OAAA,CAACH,IAAI;kBAAC8E,IAAI,EAAC,OAAO;kBAACC,GAAG,EAAC;gBAA2B;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,GACJlD,KAAK,gBACPV,OAAA,CAACF,KAAK;gBACJiD,OAAO,EAAC,QAAQ;gBAChB8B,WAAW,EAAEnE,KAAM;gBACnB4C,IAAI,EAAC,OAAO;gBACZwB,QAAQ;gBACRf,SAAS,EAAC;cAA2B;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,gBAEF5D,OAAA,CAAAE,SAAA;gBAAAsD,QAAA,gBAEExD,OAAA;kBAAK+D,SAAS,EAAC,gBAAgB;kBAAAP,QAAA,eAC7BxD,OAAA,CAACL,WAAW;oBACVqE,MAAM,EAAC,YAAY;oBACnBC,MAAM,EAAC,WAAW;oBAClBC,UAAU,EAAE,IAAK;oBACjBC,SAAS,EAAE;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN5D,OAAA;kBAAK+D,SAAS,EAAC,sDAAsD;kBAAAP,QAAA,EAClElD,YAAY,CAACyE,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnCjF,OAAA;oBAA0B+D,SAAS,EAAC,iBAAiB;oBAACmB,KAAK,EAAE;sBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;oBAAI,CAAE;oBAAAzB,QAAA,eACjGxD,OAAA,CAACR,uBAAuB;sBACtB6B,EAAE,EAAE2D,WAAW,CAAC3D,EAAG;sBACnB+D,KAAK,EAAEJ,WAAW,CAACI,KAAM;sBACzBC,SAAS,EAAEL,WAAW,CAACK,SAAU;sBACjCC,QAAQ,EAAEN,WAAW,CAACM,QAAS;sBAC/BC,MAAM,EAAEP,WAAW,CAACO,MAAO;sBAC3B1D,KAAK,EAAEmD,WAAW,CAACnD,KAAM;sBACzB2D,OAAO,EAAER,WAAW,CAACQ,OAAQ;sBAC7BC,aAAa,EAAET,WAAW,CAACS,aAAc;sBACzCC,OAAO,EAAEtE,sBAAuB;sBAChC6D,KAAK,EAAEA;oBAAM;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC,GAZMoB,WAAW,CAAC3D,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAanB,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGLhD,UAAU,CAACE,KAAK,GAAG,CAAC,iBACnBd,OAAA;kBAAK+D,SAAS,EAAC,2BAA2B;kBAAAP,QAAA,eACxCxD,OAAA,CAACJ,UAAU;oBACT+F,OAAO,EAAE/E,UAAU,CAACG,IAAK;oBACzBD,KAAK,EAAEF,UAAU,CAACE,KAAM;oBACxB8E,QAAQ,EAAEhF,UAAU,CAACI,KAAM;oBAC3B6E,QAAQ,EAAE5C,gBAAiB;oBAC3B6C,eAAe,EAAE,KAAM;oBACvBC,eAAe;oBACfC,SAAS,EAAGlF,KAAK,IAAK,SAASA,KAAK,UAAW;oBAC/CiD,SAAS,EAAC;kBAAmC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA,eACD,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA,CAACP,mBAAmB;YAClBwG,MAAM,EAAE5C,aAAc;YACtBU,SAAS,EAAC;UAAU;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5D,OAAA,CAACN,kBAAkB;QACjBwG,eAAe,EAAC,OAAO;QACvB3C,WAAW,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACxD,EAAA,CA5QID,iBAA2B;EAAA,QACNZ,WAAW;AAAA;AAAA4G,EAAA,GADhChG,iBAA2B;AA8QjC,eAAeA,iBAAiB;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}