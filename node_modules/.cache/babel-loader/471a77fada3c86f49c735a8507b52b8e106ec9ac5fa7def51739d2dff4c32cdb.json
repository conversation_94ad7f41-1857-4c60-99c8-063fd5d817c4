{"ast": null, "code": "import * as React from 'react';\nfunction toArray(val) {\n  return val ? Array.isArray(val) ? val : [val] : [];\n}\nexport default function useAction(mobile, action, showAction, hideAction) {\n  return React.useMemo(function () {\n    var mergedShowAction = toArray(showAction !== null && showAction !== void 0 ? showAction : action);\n    var mergedHideAction = toArray(hideAction !== null && hideAction !== void 0 ? hideAction : action);\n    var showActionSet = new Set(mergedShowAction);\n    var hideActionSet = new Set(mergedHideAction);\n    if (mobile) {\n      if (showActionSet.has('hover')) {\n        showActionSet.delete('hover');\n        showActionSet.add('click');\n      }\n      if (hideActionSet.has('hover')) {\n        hideActionSet.delete('hover');\n        hideActionSet.add('click');\n      }\n    }\n    return [showActionSet, hideActionSet];\n  }, [mobile, action, showAction, hideAction]);\n}", "map": {"version": 3, "names": ["React", "toArray", "val", "Array", "isArray", "useAction", "mobile", "action", "showAction", "hideAction", "useMemo", "mergedShowAction", "mergedHideAction", "showActionSet", "Set", "hideActionSet", "has", "delete", "add"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/@rc-component/trigger/es/hooks/useAction.js"], "sourcesContent": ["import * as React from 'react';\nfunction toArray(val) {\n  return val ? Array.isArray(val) ? val : [val] : [];\n}\nexport default function useAction(mobile, action, showAction, hideAction) {\n  return React.useMemo(function () {\n    var mergedShowAction = toArray(showAction !== null && showAction !== void 0 ? showAction : action);\n    var mergedHideAction = toArray(hideAction !== null && hideAction !== void 0 ? hideAction : action);\n    var showActionSet = new Set(mergedShowAction);\n    var hideActionSet = new Set(mergedHideAction);\n    if (mobile) {\n      if (showActionSet.has('hover')) {\n        showActionSet.delete('hover');\n        showActionSet.add('click');\n      }\n      if (hideActionSet.has('hover')) {\n        hideActionSet.delete('hover');\n        hideActionSet.add('click');\n      }\n    }\n    return [showActionSet, hideActionSet];\n  }, [mobile, action, showAction, hideAction]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOA,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC,GAAG,EAAE;AACpD;AACA,eAAe,SAASG,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACxE,OAAOT,KAAK,CAACU,OAAO,CAAC,YAAY;IAC/B,IAAIC,gBAAgB,GAAGV,OAAO,CAACO,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGD,MAAM,CAAC;IAClG,IAAIK,gBAAgB,GAAGX,OAAO,CAACQ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGF,MAAM,CAAC;IAClG,IAAIM,aAAa,GAAG,IAAIC,GAAG,CAACH,gBAAgB,CAAC;IAC7C,IAAII,aAAa,GAAG,IAAID,GAAG,CAACF,gBAAgB,CAAC;IAC7C,IAAIN,MAAM,EAAE;MACV,IAAIO,aAAa,CAACG,GAAG,CAAC,OAAO,CAAC,EAAE;QAC9BH,aAAa,CAACI,MAAM,CAAC,OAAO,CAAC;QAC7BJ,aAAa,CAACK,GAAG,CAAC,OAAO,CAAC;MAC5B;MACA,IAAIH,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE;QAC9BD,aAAa,CAACE,MAAM,CAAC,OAAO,CAAC;QAC7BF,aAAa,CAACG,GAAG,CAAC,OAAO,CAAC;MAC5B;IACF;IACA,OAAO,CAACL,aAAa,EAAEE,aAAa,CAAC;EACvC,CAAC,EAAE,CAACT,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,CAAC,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}