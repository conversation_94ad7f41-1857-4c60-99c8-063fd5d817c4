{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport classNames from 'classnames';\n/**\n * Fill component to provided the scroll content real height.\n */\nvar Filler = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var height = _ref.height,\n    offsetY = _ref.offsetY,\n    offsetX = _ref.offsetX,\n    children = _ref.children,\n    prefixCls = _ref.prefixCls,\n    onInnerResize = _ref.onInnerResize,\n    innerProps = _ref.innerProps,\n    rtl = _ref.rtl,\n    extra = _ref.extra;\n  var outerStyle = {};\n  var innerStyle = {\n    display: 'flex',\n    flexDirection: 'column'\n  };\n  if (offsetY !== undefined) {\n    // Not set `width` since this will break `sticky: right`\n    outerStyle = {\n      height: height,\n      position: 'relative',\n      overflow: 'hidden'\n    };\n    innerStyle = _objectSpread(_objectSpread({}, innerStyle), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n      transform: \"translateY(\".concat(offsetY, \"px)\")\n    }, rtl ? 'marginRight' : 'marginLeft', -offsetX), \"position\", 'absolute'), \"left\", 0), \"right\", 0), \"top\", 0));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: outerStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: function onResize(_ref2) {\n      var offsetHeight = _ref2.offsetHeight;\n      if (offsetHeight && onInnerResize) {\n        onInnerResize();\n      }\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: innerStyle,\n    className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-holder-inner\"), prefixCls)),\n    ref: ref\n  }, innerProps), children, extra)));\n});\nFiller.displayName = 'Filler';\nexport default Filler;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "React", "ResizeObserver", "classNames", "Filler", "forwardRef", "_ref", "ref", "height", "offsetY", "offsetX", "children", "prefixCls", "onInnerResize", "innerProps", "rtl", "extra", "outerStyle", "innerStyle", "display", "flexDirection", "undefined", "position", "overflow", "transform", "concat", "createElement", "style", "onResize", "_ref2", "offsetHeight", "className", "displayName"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-virtual-list/es/Filler.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport classNames from 'classnames';\n/**\n * Fill component to provided the scroll content real height.\n */\nvar Filler = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var height = _ref.height,\n    offsetY = _ref.offsetY,\n    offsetX = _ref.offsetX,\n    children = _ref.children,\n    prefixCls = _ref.prefixCls,\n    onInnerResize = _ref.onInnerResize,\n    innerProps = _ref.innerProps,\n    rtl = _ref.rtl,\n    extra = _ref.extra;\n  var outerStyle = {};\n  var innerStyle = {\n    display: 'flex',\n    flexDirection: 'column'\n  };\n  if (offsetY !== undefined) {\n    // Not set `width` since this will break `sticky: right`\n    outerStyle = {\n      height: height,\n      position: 'relative',\n      overflow: 'hidden'\n    };\n    innerStyle = _objectSpread(_objectSpread({}, innerStyle), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n      transform: \"translateY(\".concat(offsetY, \"px)\")\n    }, rtl ? 'marginRight' : 'marginLeft', -offsetX), \"position\", 'absolute'), \"left\", 0), \"right\", 0), \"top\", 0));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: outerStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: function onResize(_ref2) {\n      var offsetHeight = _ref2.offsetHeight;\n      if (offsetHeight && onInnerResize) {\n        onInnerResize();\n      }\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: innerStyle,\n    className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-holder-inner\"), prefixCls)),\n    ref: ref\n  }, innerProps), children, extra)));\n});\nFiller.displayName = 'Filler';\nexport default Filler;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,UAAU,MAAM,YAAY;AACnC;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EAC9D,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACtBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;IACtBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,SAAS,GAAGN,IAAI,CAACM,SAAS;IAC1BC,aAAa,GAAGP,IAAI,CAACO,aAAa;IAClCC,UAAU,GAAGR,IAAI,CAACQ,UAAU;IAC5BC,GAAG,GAAGT,IAAI,CAACS,GAAG;IACdC,KAAK,GAAGV,IAAI,CAACU,KAAK;EACpB,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB,IAAIC,UAAU,GAAG;IACfC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE;EACjB,CAAC;EACD,IAAIX,OAAO,KAAKY,SAAS,EAAE;IACzB;IACAJ,UAAU,GAAG;MACXT,MAAM,EAAEA,MAAM;MACdc,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACDL,UAAU,GAAGlB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,UAAU,CAAC,EAAE,CAAC,CAAC,EAAEnB,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC;MAC5IyB,SAAS,EAAE,aAAa,CAACC,MAAM,CAAChB,OAAO,EAAE,KAAK;IAChD,CAAC,EAAEM,GAAG,GAAG,aAAa,GAAG,YAAY,EAAE,CAACL,OAAO,CAAC,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;EAChH;EACA,OAAO,aAAaT,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAEV;EACT,CAAC,EAAE,aAAahB,KAAK,CAACyB,aAAa,CAACxB,cAAc,EAAE;IAClD0B,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;MACjC,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;MACrC,IAAIA,YAAY,IAAIjB,aAAa,EAAE;QACjCA,aAAa,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE,aAAaZ,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE5B,QAAQ,CAAC;IAClD6B,KAAK,EAAET,UAAU;IACjBa,SAAS,EAAE5B,UAAU,CAACJ,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0B,MAAM,CAACb,SAAS,EAAE,eAAe,CAAC,EAAEA,SAAS,CAAC,CAAC;IAC5FL,GAAG,EAAEA;EACP,CAAC,EAAEO,UAAU,CAAC,EAAEH,QAAQ,EAAEK,KAAK,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC;AACFZ,MAAM,CAAC4B,WAAW,GAAG,QAAQ;AAC7B,eAAe5B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}