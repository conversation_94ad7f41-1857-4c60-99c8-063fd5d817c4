{"ast": null, "code": "import { linearish } from \"./linear.js\";\nimport { copy, transformer } from \"./continuous.js\";\nimport { initRange } from \"./init.js\";\nfunction transformSymlog(c) {\n  return function (x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\nfunction transformSymexp(c) {\n  return function (x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\nexport function symlogish(transform) {\n  var c = 1,\n    scale = transform(transformSymlog(c), transformSymexp(c));\n  scale.constant = function (_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n  return linearish(scale);\n}\nexport default function symlog() {\n  var scale = symlogish(transformer());\n  scale.copy = function () {\n    return copy(scale, symlog()).constant(scale.constant());\n  };\n  return initRange.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["linearish", "copy", "transformer", "initRange", "transformSymlog", "c", "x", "Math", "sign", "log1p", "abs", "transformSymexp", "expm1", "symlogish", "transform", "scale", "constant", "_", "arguments", "length", "symlog", "apply"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-scale/src/symlog.js"], "sourcesContent": ["import {linearish} from \"./linear.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformSymlog(c) {\n  return function(x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function(x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nexport function symlogish(transform) {\n  var c = 1, scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function(_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return linearish(scale);\n}\n\nexport default function symlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, symlog()).constant(scale.constant());\n  };\n\n  return initRange.apply(scale, arguments);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,aAAa;AACrC,SAAQC,IAAI,EAAEC,WAAW,QAAO,iBAAiB;AACjD,SAAQC,SAAS,QAAO,WAAW;AAEnC,SAASC,eAAeA,CAACC,CAAC,EAAE;EAC1B,OAAO,UAASC,CAAC,EAAE;IACjB,OAAOC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,GAAGC,IAAI,CAACE,KAAK,CAACF,IAAI,CAACG,GAAG,CAACJ,CAAC,GAAGD,CAAC,CAAC,CAAC;EACnD,CAAC;AACH;AAEA,SAASM,eAAeA,CAACN,CAAC,EAAE;EAC1B,OAAO,UAASC,CAAC,EAAE;IACjB,OAAOC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,GAAGC,IAAI,CAACK,KAAK,CAACL,IAAI,CAACG,GAAG,CAACJ,CAAC,CAAC,CAAC,GAAGD,CAAC;EACnD,CAAC;AACH;AAEA,OAAO,SAASQ,SAASA,CAACC,SAAS,EAAE;EACnC,IAAIT,CAAC,GAAG,CAAC;IAAEU,KAAK,GAAGD,SAAS,CAACV,eAAe,CAACC,CAAC,CAAC,EAAEM,eAAe,CAACN,CAAC,CAAC,CAAC;EAEpEU,KAAK,CAACC,QAAQ,GAAG,UAASC,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,GAAGL,SAAS,CAACV,eAAe,CAACC,CAAC,GAAG,CAACY,CAAC,CAAC,EAAEN,eAAe,CAACN,CAAC,CAAC,CAAC,GAAGA,CAAC;EACtF,CAAC;EAED,OAAOL,SAAS,CAACe,KAAK,CAAC;AACzB;AAEA,eAAe,SAASK,MAAMA,CAAA,EAAG;EAC/B,IAAIL,KAAK,GAAGF,SAAS,CAACX,WAAW,CAAC,CAAC,CAAC;EAEpCa,KAAK,CAACd,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACc,KAAK,EAAEK,MAAM,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAACD,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EACzD,CAAC;EAED,OAAOb,SAAS,CAACkB,KAAK,CAACN,KAAK,EAAEG,SAAS,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}