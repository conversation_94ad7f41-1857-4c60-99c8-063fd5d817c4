{"ast": null, "code": "import { Path } from \"d3-path\";\nexport function withPath(shape) {\n  let digits = 3;\n  shape.digits = function (_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n  return () => new Path(digits);\n}", "map": {"version": 3, "names": ["Path", "with<PERSON><PERSON>", "shape", "digits", "_", "arguments", "length", "d", "Math", "floor", "RangeError"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-shape/src/path.js"], "sourcesContent": ["import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,SAAS;AAE5B,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,IAAIC,MAAM,GAAG,CAAC;EAEdD,KAAK,CAACC,MAAM,GAAG,UAASC,CAAC,EAAE;IACzB,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE,OAAOH,MAAM;IACpC,IAAIC,CAAC,IAAI,IAAI,EAAE;MACbD,MAAM,GAAG,IAAI;IACf,CAAC,MAAM;MACL,MAAMI,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;MACvB,IAAI,EAAEG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,IAAIG,UAAU,CAAC,mBAAmBN,CAAC,EAAE,CAAC;MAC3DD,MAAM,GAAGI,CAAC;IACZ;IACA,OAAOL,KAAK;EACd,CAAC;EAED,OAAO,MAAM,IAAIF,IAAI,CAACG,MAAM,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}