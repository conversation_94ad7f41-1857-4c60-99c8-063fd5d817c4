{"ast": null, "code": "var Set = require('./_Set'),\n  noop = require('./noop'),\n  setToArray = require('./_setToArray');\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Creates a set object of `values`.\n *\n * @private\n * @param {Array} values The values to add to the set.\n * @returns {Object} Returns the new set.\n */\nvar createSet = !(Set && 1 / setToArray(new Set([, -0]))[1] == INFINITY) ? noop : function (values) {\n  return new Set(values);\n};\nmodule.exports = createSet;", "map": {"version": 3, "names": ["Set", "require", "noop", "setToArray", "INFINITY", "createSet", "values", "module", "exports"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lodash/_createSet.js"], "sourcesContent": ["var Set = require('./_Set'),\n    noop = require('./noop'),\n    setToArray = require('./_setToArray');\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Creates a set object of `values`.\n *\n * @private\n * @param {Array} values The values to add to the set.\n * @returns {Object} Returns the new set.\n */\nvar createSet = !(Set && (1 / setToArray(new Set([,-0]))[1]) == INFINITY) ? noop : function(values) {\n  return new Set(values);\n};\n\nmodule.exports = createSet;\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,CAAC,QAAQ,CAAC;EACvBC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;EACxBE,UAAU,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIG,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,EAAEL,GAAG,IAAK,CAAC,GAAGG,UAAU,CAAC,IAAIH,GAAG,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAKI,QAAQ,CAAC,GAAGF,IAAI,GAAG,UAASI,MAAM,EAAE;EAClG,OAAO,IAAIN,GAAG,CAACM,MAAM,CAAC;AACxB,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}