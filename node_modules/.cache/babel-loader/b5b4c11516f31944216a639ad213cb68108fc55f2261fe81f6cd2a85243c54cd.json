{"ast": null, "code": "import classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport * as React from 'react';\nexport default function MultipleDates(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    onRemove = props.onRemove,\n    _props$removeIcon = props.removeIcon,\n    removeIcon = _props$removeIcon === void 0 ? '×' : _props$removeIcon,\n    formatDate = props.formatDate,\n    disabled = props.disabled,\n    maxTagCount = props.maxTagCount,\n    placeholder = props.placeholder;\n  var selectorCls = \"\".concat(prefixCls, \"-selector\");\n  var selectionCls = \"\".concat(prefixCls, \"-selection\");\n  var overflowCls = \"\".concat(selectionCls, \"-overflow\");\n\n  // ========================= Item =========================\n  function renderSelector(content, onClose) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(\"\".concat(selectionCls, \"-item\")),\n      title: typeof content === 'string' ? content : null\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(selectionCls, \"-item-content\")\n    }, content), !disabled && onClose && /*#__PURE__*/React.createElement(\"span\", {\n      onMouseDown: function onMouseDown(e) {\n        e.preventDefault();\n      },\n      onClick: onClose,\n      className: \"\".concat(selectionCls, \"-item-remove\")\n    }, removeIcon));\n  }\n  function renderItem(date) {\n    var displayLabel = formatDate(date);\n    var onClose = function onClose(event) {\n      if (event) event.stopPropagation();\n      onRemove(date);\n    };\n    return renderSelector(displayLabel, onClose);\n  }\n\n  // ========================= Rest =========================\n  function renderRest(omittedValues) {\n    var content = \"+ \".concat(omittedValues.length, \" ...\");\n    return renderSelector(content);\n  }\n\n  // ======================== Render ========================\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: selectorCls\n  }, /*#__PURE__*/React.createElement(Overflow, {\n    prefixCls: overflowCls,\n    data: value,\n    renderItem: renderItem,\n    renderRest: renderRest\n    // suffix={inputNode}\n    ,\n\n    itemKey: function itemKey(date) {\n      return formatDate(date);\n    },\n    maxCount: maxTagCount\n  }), !value.length && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-placeholder\")\n  }, placeholder));\n}", "map": {"version": 3, "names": ["classNames", "Overflow", "React", "MultipleDates", "props", "prefixCls", "value", "onRemove", "_props$removeIcon", "removeIcon", "formatDate", "disabled", "maxTag<PERSON>ount", "placeholder", "selectorCls", "concat", "selectionCls", "overflowCls", "renderSelector", "content", "onClose", "createElement", "className", "title", "onMouseDown", "e", "preventDefault", "onClick", "renderItem", "date", "displayLabel", "event", "stopPropagation", "renderRest", "omitted<PERSON><PERSON><PERSON>", "length", "data", "itemKey", "maxCount"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-picker/es/PickerInput/Selector/SingleSelector/MultipleDates.js"], "sourcesContent": ["import classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport * as React from 'react';\nexport default function MultipleDates(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    onRemove = props.onRemove,\n    _props$removeIcon = props.removeIcon,\n    removeIcon = _props$removeIcon === void 0 ? '×' : _props$removeIcon,\n    formatDate = props.formatDate,\n    disabled = props.disabled,\n    maxTagCount = props.maxTagCount,\n    placeholder = props.placeholder;\n  var selectorCls = \"\".concat(prefixCls, \"-selector\");\n  var selectionCls = \"\".concat(prefixCls, \"-selection\");\n  var overflowCls = \"\".concat(selectionCls, \"-overflow\");\n\n  // ========================= Item =========================\n  function renderSelector(content, onClose) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(\"\".concat(selectionCls, \"-item\")),\n      title: typeof content === 'string' ? content : null\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(selectionCls, \"-item-content\")\n    }, content), !disabled && onClose && /*#__PURE__*/React.createElement(\"span\", {\n      onMouseDown: function onMouseDown(e) {\n        e.preventDefault();\n      },\n      onClick: onClose,\n      className: \"\".concat(selectionCls, \"-item-remove\")\n    }, removeIcon));\n  }\n  function renderItem(date) {\n    var displayLabel = formatDate(date);\n    var onClose = function onClose(event) {\n      if (event) event.stopPropagation();\n      onRemove(date);\n    };\n    return renderSelector(displayLabel, onClose);\n  }\n\n  // ========================= Rest =========================\n  function renderRest(omittedValues) {\n    var content = \"+ \".concat(omittedValues.length, \" ...\");\n    return renderSelector(content);\n  }\n\n  // ======================== Render ========================\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: selectorCls\n  }, /*#__PURE__*/React.createElement(Overflow, {\n    prefixCls: overflowCls,\n    data: value,\n    renderItem: renderItem,\n    renderRest: renderRest\n    // suffix={inputNode}\n    ,\n    itemKey: function itemKey(date) {\n      return formatDate(date);\n    },\n    maxCount: maxTagCount\n  }), !value.length && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-placeholder\")\n  }, placeholder));\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,iBAAiB,GAAGJ,KAAK,CAACK,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;IACnEE,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,WAAW,GAAGT,KAAK,CAACS,WAAW;EACjC,IAAIC,WAAW,GAAG,EAAE,CAACC,MAAM,CAACV,SAAS,EAAE,WAAW,CAAC;EACnD,IAAIW,YAAY,GAAG,EAAE,CAACD,MAAM,CAACV,SAAS,EAAE,YAAY,CAAC;EACrD,IAAIY,WAAW,GAAG,EAAE,CAACF,MAAM,CAACC,YAAY,EAAE,WAAW,CAAC;;EAEtD;EACA,SAASE,cAAcA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACxC,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAEtB,UAAU,CAAC,EAAE,CAACe,MAAM,CAACC,YAAY,EAAE,OAAO,CAAC,CAAC;MACvDO,KAAK,EAAE,OAAOJ,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG;IACjD,CAAC,EAAE,aAAajB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC1CC,SAAS,EAAE,EAAE,CAACP,MAAM,CAACC,YAAY,EAAE,eAAe;IACpD,CAAC,EAAEG,OAAO,CAAC,EAAE,CAACR,QAAQ,IAAIS,OAAO,IAAI,aAAalB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC5EG,WAAW,EAAE,SAASA,WAAWA,CAACC,CAAC,EAAE;QACnCA,CAAC,CAACC,cAAc,CAAC,CAAC;MACpB,CAAC;MACDC,OAAO,EAAEP,OAAO;MAChBE,SAAS,EAAE,EAAE,CAACP,MAAM,CAACC,YAAY,EAAE,cAAc;IACnD,CAAC,EAAEP,UAAU,CAAC,CAAC;EACjB;EACA,SAASmB,UAAUA,CAACC,IAAI,EAAE;IACxB,IAAIC,YAAY,GAAGpB,UAAU,CAACmB,IAAI,CAAC;IACnC,IAAIT,OAAO,GAAG,SAASA,OAAOA,CAACW,KAAK,EAAE;MACpC,IAAIA,KAAK,EAAEA,KAAK,CAACC,eAAe,CAAC,CAAC;MAClCzB,QAAQ,CAACsB,IAAI,CAAC;IAChB,CAAC;IACD,OAAOX,cAAc,CAACY,YAAY,EAAEV,OAAO,CAAC;EAC9C;;EAEA;EACA,SAASa,UAAUA,CAACC,aAAa,EAAE;IACjC,IAAIf,OAAO,GAAG,IAAI,CAACJ,MAAM,CAACmB,aAAa,CAACC,MAAM,EAAE,MAAM,CAAC;IACvD,OAAOjB,cAAc,CAACC,OAAO,CAAC;EAChC;;EAEA;;EAEA,OAAO,aAAajB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAER;EACb,CAAC,EAAE,aAAaZ,KAAK,CAACmB,aAAa,CAACpB,QAAQ,EAAE;IAC5CI,SAAS,EAAEY,WAAW;IACtBmB,IAAI,EAAE9B,KAAK;IACXsB,UAAU,EAAEA,UAAU;IACtBK,UAAU,EAAEA;IACZ;IAAA;;IAEAI,OAAO,EAAE,SAASA,OAAOA,CAACR,IAAI,EAAE;MAC9B,OAAOnB,UAAU,CAACmB,IAAI,CAAC;IACzB,CAAC;IACDS,QAAQ,EAAE1B;EACZ,CAAC,CAAC,EAAE,CAACN,KAAK,CAAC6B,MAAM,IAAI,aAAajC,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IAC5DC,SAAS,EAAE,EAAE,CAACP,MAAM,CAACV,SAAS,EAAE,wBAAwB;EAC1D,CAAC,EAAEQ,WAAW,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}