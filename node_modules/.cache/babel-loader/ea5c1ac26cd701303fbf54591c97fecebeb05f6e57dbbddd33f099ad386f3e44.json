{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    measureColumnWidth = props.measureColumnWidth;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns', 'getRowKey', 'expandedKeys', 'childrenColumnName', 'emptyNode']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    childrenColumnName = _useContext.childrenColumnName,\n    emptyNode = _useContext.emptyNode;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index;\n      var key = getRowKey(record, idx);\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: key,\n        rowKey: key,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        indent: indent\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nif (process.env.NODE_ENV !== 'production') {\n  Body.displayName = 'Body';\n}\nexport default responseImmutable(Body);", "map": {"version": 3, "names": ["useContext", "React", "PerfContext", "TableContext", "responseImmutable", "useFlattenRecords", "devRenderTimes", "getColumnsKey", "BodyRow", "ExpandedRow", "MeasureRow", "Body", "props", "process", "env", "NODE_ENV", "data", "measureColumnWidth", "_useContext", "prefixCls", "getComponent", "onColumnResize", "flattenColumns", "getRowKey", "expandedKeys", "childrenColumnName", "emptyNode", "flattenData", "perfRef", "useRef", "renderWithProps", "WrapperComponent", "trComponent", "tdComponent", "thComponent", "rows", "length", "map", "item", "idx", "record", "indent", "renderIndex", "index", "key", "createElement", "<PERSON><PERSON><PERSON>", "rowComponent", "cellComponent", "scopeCellComponent", "expanded", "className", "concat", "component", "colSpan", "isEmpty", "columnsKey", "Provider", "value", "current", "displayName"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/rc-table/es/Body/index.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    measureColumnWidth = props.measureColumnWidth;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns', 'getRowKey', 'expandedKeys', 'childrenColumnName', 'emptyNode']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    childrenColumnName = _useContext.childrenColumnName,\n    emptyNode = _useContext.emptyNode;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index;\n      var key = getRowKey(record, idx);\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: key,\n        rowKey: key,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        indent: indent\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nif (process.env.NODE_ENV !== 'production') {\n  Body.displayName = 'Body';\n}\nexport default responseImmutable(Body);"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCT,cAAc,CAACM,KAAK,CAAC;EACvB;EACA,IAAII,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACnBC,kBAAkB,GAAGL,KAAK,CAACK,kBAAkB;EAC/C,IAAIC,WAAW,GAAGlB,UAAU,CAACG,YAAY,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAAC;IAC3KgB,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,YAAY,GAAGF,WAAW,CAACE,YAAY;IACvCC,cAAc,GAAGH,WAAW,CAACG,cAAc;IAC3CC,cAAc,GAAGJ,WAAW,CAACI,cAAc;IAC3CC,SAAS,GAAGL,WAAW,CAACK,SAAS;IACjCC,YAAY,GAAGN,WAAW,CAACM,YAAY;IACvCC,kBAAkB,GAAGP,WAAW,CAACO,kBAAkB;IACnDC,SAAS,GAAGR,WAAW,CAACQ,SAAS;EACnC,IAAIC,WAAW,GAAGtB,iBAAiB,CAACW,IAAI,EAAES,kBAAkB,EAAED,YAAY,EAAED,SAAS,CAAC;;EAEtF;EACA,IAAIK,OAAO,GAAG3B,KAAK,CAAC4B,MAAM,CAAC;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,IAAIC,gBAAgB,GAAGX,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;EACjE,IAAIY,WAAW,GAAGZ,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;EACrD,IAAIa,WAAW,GAAGb,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACtD,IAAIc,WAAW,GAAGd,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACtD,IAAIe,IAAI;EACR,IAAInB,IAAI,CAACoB,MAAM,EAAE;IACfD,IAAI,GAAGR,WAAW,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;MAC1C,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;QACtBC,MAAM,GAAGH,IAAI,CAACG,MAAM;QACpBC,WAAW,GAAGJ,IAAI,CAACK,KAAK;MAC1B,IAAIC,GAAG,GAAGrB,SAAS,CAACiB,MAAM,EAAED,GAAG,CAAC;MAChC,OAAO,aAAatC,KAAK,CAAC4C,aAAa,CAACrC,OAAO,EAAE;QAC/CoC,GAAG,EAAEA,GAAG;QACRE,MAAM,EAAEF,GAAG;QACXJ,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAEJ,GAAG;QACVG,WAAW,EAAEA,WAAW;QACxBK,YAAY,EAAEf,WAAW;QACzBgB,aAAa,EAAEf,WAAW;QAC1BgB,kBAAkB,EAAEf,WAAW;QAC/BO,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,IAAI,GAAG,aAAalC,KAAK,CAAC4C,aAAa,CAACpC,WAAW,EAAE;MACnDyC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACjC,SAAS,EAAE,cAAc,CAAC;MAC/CA,SAAS,EAAEA,SAAS;MACpBkC,SAAS,EAAErB,WAAW;MACtBgB,aAAa,EAAEf,WAAW;MAC1BqB,OAAO,EAAEhC,cAAc,CAACc,MAAM;MAC9BmB,OAAO,EAAE;IACX,CAAC,EAAE7B,SAAS,CAAC;EACf;EACA,IAAI8B,UAAU,GAAGjD,aAAa,CAACe,cAAc,CAAC;EAC9C,OAAO,aAAarB,KAAK,CAAC4C,aAAa,CAAC3C,WAAW,CAACuD,QAAQ,EAAE;IAC5DC,KAAK,EAAE9B,OAAO,CAAC+B;EACjB,CAAC,EAAE,aAAa1D,KAAK,CAAC4C,aAAa,CAACd,gBAAgB,EAAE;IACpDoB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACjC,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEF,kBAAkB,IAAI,aAAahB,KAAK,CAAC4C,aAAa,CAACnC,UAAU,EAAE;IACpES,SAAS,EAAEA,SAAS;IACpBqC,UAAU,EAAEA,UAAU;IACtBnC,cAAc,EAAEA;EAClB,CAAC,CAAC,EAAEc,IAAI,CAAC,CAAC;AACZ;AACA,IAAItB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,IAAI,CAACiD,WAAW,GAAG,MAAM;AAC3B;AACA,eAAexD,iBAAiB,CAACO,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}