{"ast": null, "code": "export { Path, path, pathRound } from \"./path.js\";", "map": {"version": 3, "names": ["Path", "path", "pathRound"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/d3-path/src/index.js"], "sourcesContent": ["export {Path, path, pathRound} from \"./path.js\";\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,IAAI,EAAEC,SAAS,QAAO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}